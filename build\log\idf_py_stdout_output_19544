-- Existing sdkconfig 'C:/Users/<USER>/Desktop/i2c_oled/sdkconfig' renamed to 'C:/Users/<USER>/Desktop/i2c_oled/sdkconfig.old'.
-- Found Git: D:/ESP_IDF/idf_tools/tools/idf-git/2.39.2/cmd/git.exe (found version "2.39.2.windows.1")
-- The C compiler identification is GNU 13.2.0
-- The CXX compiler identification is GNU 13.2.0
-- The ASM compiler identification is GNU
-- Found assembler: D:/ESP_IDF/idf_tools/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc.exe
-- Detecting C compiler ABI info
-- Detecting C compiler ABI info - done
-- Check for working C compiler: D:/ESP_IDF/idf_tools/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc.exe - skipped
-- Detecting C compile features
-- Detecting C compile features - done
-- Detecting CXX compiler ABI info
-- Detecting CXX compiler ABI info - done
-- Check for working CXX compiler: D:/ESP_IDF/idf_tools/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-g++.exe - skipped
-- Detecting CXX compile features
-- Detecting CXX compile features - done
-- git rev-parse returned 'fatal: not a git repository (or any of the parent directories): .git'
-- Could not use 'git describe' to determine PROJECT_VER.
-- Building ESP-IDF components for target esp32s3
NOTICE: Updating lock file at C:\Users\<USER>\Desktop\i2c_oled\dependencies.lock
NOTICE: Processing 4 dependencies:
NOTICE: [1/4] espressif/esp_lcd_sh1107 (1.1.0)
NOTICE: [2/4] espressif/esp_lvgl_port (1.4.0)
NOTICE: [3/4] lvgl/lvgl (8.3.11)
NOTICE: [4/4] idf (5.3.4)
-- Project sdkconfig file C:/Users/<USER>/Desktop/i2c_oled/sdkconfig
Loading defaults file C:/Users/<USER>/Desktop/i2c_oled/sdkconfig.defaults...
-- Compiler supported targets: xtensa-esp-elf
-- Found Python3: d:/ESP_IDF/idf_tools/python_env/idf5.3_py3.11_env/Scripts/python.exe (found version "3.11.2") found components: Interpreter
-- Performing Test CMAKE_HAVE_LIBC_PTHREAD
-- Performing Test CMAKE_HAVE_LIBC_PTHREAD - Success
-- Found Threads: TRUE
-- Performing Test C_COMPILER_SUPPORTS_WFORMAT_SIGNEDNESS
-- Performing Test C_COMPILER_SUPPORTS_WFORMAT_SIGNEDNESS - Success
-- USING O3
-- App "i2c_oled" version: 1
-- Adding linker script C:/Users/<USER>/Desktop/i2c_oled/build/esp-idf/esp_system/ld/memory.ld
-- Adding linker script C:/Users/<USER>/Desktop/i2c_oled/build/esp-idf/esp_system/ld/sections.ld.in
-- Adding linker script D:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/esp32s3/ld/esp32s3.rom.ld
-- Adding linker script D:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/esp32s3/ld/esp32s3.rom.api.ld
-- Adding linker script D:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/esp32s3/ld/esp32s3.rom.bt_funcs.ld
-- Adding linker script D:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/esp32s3/ld/esp32s3.rom.libgcc.ld
-- Adding linker script D:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/esp32s3/ld/esp32s3.rom.wdt.ld
-- Adding linker script D:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/esp32s3/ld/esp32s3.rom.version.ld
-- Adding linker script D:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/esp32s3/ld/esp32s3.rom.ble_master.ld
-- Adding linker script D:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/esp32s3/ld/esp32s3.rom.ble_50.ld
-- Adding linker script D:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/esp32s3/ld/esp32s3.rom.ble_smp.ld
-- Adding linker script D:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/esp32s3/ld/esp32s3.rom.ble_dtm.ld
-- Adding linker script D:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/esp32s3/ld/esp32s3.rom.ble_test.ld
-- Adding linker script D:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/esp32s3/ld/esp32s3.rom.ble_scan.ld
-- Adding linker script D:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/esp32s3/ld/esp32s3.rom.newlib.ld
-- Adding linker script D:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3/ld/esp32s3.peripherals.ld
-- Components: app_trace app_update bootloader bootloader_support bt cmock console cxx driver efuse esp-tls esp_adc esp_app_format esp_bootloader_format esp_coex esp_common esp_driver_ana_cmpr esp_driver_cam esp_driver_dac esp_driver_gpio esp_driver_gptimer esp_driver_i2c esp_driver_i2s esp_driver_isp esp_driver_jpeg esp_driver_ledc esp_driver_mcpwm esp_driver_parlio esp_driver_pcnt esp_driver_ppa esp_driver_rmt esp_driver_sdio esp_driver_sdm esp_driver_sdmmc esp_driver_sdspi esp_driver_spi esp_driver_touch_sens esp_driver_tsens esp_driver_uart esp_driver_usb_serial_jtag esp_eth esp_event esp_gdbstub esp_hid esp_http_client esp_http_server esp_https_ota esp_https_server esp_hw_support esp_lcd esp_local_ctrl esp_mm esp_netif esp_netif_stack esp_partition esp_phy esp_pm esp_psram esp_ringbuf esp_rom esp_system esp_timer esp_vfs_console esp_wifi espcoredump espressif__esp_lcd_sh1107 espressif__esp_lvgl_port esptool_py fatfs freertos hal heap http_parser idf_test ieee802154 json log lvgl__lvgl lwip main mbedtls mqtt newlib nvs_flash nvs_sec_provider openthread partition_table perfmon protobuf-c protocomm pthread sdmmc soc spi_flash spiffs tcp_transport touch_element ulp unity usb vfs wear_levelling wifi_provisioning wpa_supplicant xtensa
-- Component paths: D:/ESP_IDF/v5.3.4/esp-idf/components/app_trace D:/ESP_IDF/v5.3.4/esp-idf/components/app_update D:/ESP_IDF/v5.3.4/esp-idf/components/bootloader D:/ESP_IDF/v5.3.4/esp-idf/components/bootloader_support D:/ESP_IDF/v5.3.4/esp-idf/components/bt D:/ESP_IDF/v5.3.4/esp-idf/components/cmock D:/ESP_IDF/v5.3.4/esp-idf/components/console D:/ESP_IDF/v5.3.4/esp-idf/components/cxx D:/ESP_IDF/v5.3.4/esp-idf/components/driver D:/ESP_IDF/v5.3.4/esp-idf/components/efuse D:/ESP_IDF/v5.3.4/esp-idf/components/esp-tls D:/ESP_IDF/v5.3.4/esp-idf/components/esp_adc D:/ESP_IDF/v5.3.4/esp-idf/components/esp_app_format D:/ESP_IDF/v5.3.4/esp-idf/components/esp_bootloader_format D:/ESP_IDF/v5.3.4/esp-idf/components/esp_coex D:/ESP_IDF/v5.3.4/esp-idf/components/esp_common D:/ESP_IDF/v5.3.4/esp-idf/components/esp_driver_ana_cmpr D:/ESP_IDF/v5.3.4/esp-idf/components/esp_driver_cam D:/ESP_IDF/v5.3.4/esp-idf/components/esp_driver_dac D:/ESP_IDF/v5.3.4/esp-idf/components/esp_driver_gpio D:/ESP_IDF/v5.3.4/esp-idf/components/esp_driver_gptimer D:/ESP_IDF/v5.3.4/esp-idf/components/esp_driver_i2c D:/ESP_IDF/v5.3.4/esp-idf/components/esp_driver_i2s D:/ESP_IDF/v5.3.4/esp-idf/components/esp_driver_isp D:/ESP_IDF/v5.3.4/esp-idf/components/esp_driver_jpeg D:/ESP_IDF/v5.3.4/esp-idf/components/esp_driver_ledc D:/ESP_IDF/v5.3.4/esp-idf/components/esp_driver_mcpwm D:/ESP_IDF/v5.3.4/esp-idf/components/esp_driver_parlio D:/ESP_IDF/v5.3.4/esp-idf/components/esp_driver_pcnt D:/ESP_IDF/v5.3.4/esp-idf/components/esp_driver_ppa D:/ESP_IDF/v5.3.4/esp-idf/components/esp_driver_rmt D:/ESP_IDF/v5.3.4/esp-idf/components/esp_driver_sdio D:/ESP_IDF/v5.3.4/esp-idf/components/esp_driver_sdm D:/ESP_IDF/v5.3.4/esp-idf/components/esp_driver_sdmmc D:/ESP_IDF/v5.3.4/esp-idf/components/esp_driver_sdspi D:/ESP_IDF/v5.3.4/esp-idf/components/esp_driver_spi D:/ESP_IDF/v5.3.4/esp-idf/components/esp_driver_touch_sens D:/ESP_IDF/v5.3.4/esp-idf/components/esp_driver_tsens D:/ESP_IDF/v5.3.4/esp-idf/components/esp_driver_uart D:/ESP_IDF/v5.3.4/esp-idf/components/esp_driver_usb_serial_jtag D:/ESP_IDF/v5.3.4/esp-idf/components/esp_eth D:/ESP_IDF/v5.3.4/esp-idf/components/esp_event D:/ESP_IDF/v5.3.4/esp-idf/components/esp_gdbstub D:/ESP_IDF/v5.3.4/esp-idf/components/esp_hid D:/ESP_IDF/v5.3.4/esp-idf/components/esp_http_client D:/ESP_IDF/v5.3.4/esp-idf/components/esp_http_server D:/ESP_IDF/v5.3.4/esp-idf/components/esp_https_ota D:/ESP_IDF/v5.3.4/esp-idf/components/esp_https_server D:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support D:/ESP_IDF/v5.3.4/esp-idf/components/esp_lcd D:/ESP_IDF/v5.3.4/esp-idf/components/esp_local_ctrl D:/ESP_IDF/v5.3.4/esp-idf/components/esp_mm D:/ESP_IDF/v5.3.4/esp-idf/components/esp_netif D:/ESP_IDF/v5.3.4/esp-idf/components/esp_netif_stack D:/ESP_IDF/v5.3.4/esp-idf/components/esp_partition D:/ESP_IDF/v5.3.4/esp-idf/components/esp_phy D:/ESP_IDF/v5.3.4/esp-idf/components/esp_pm D:/ESP_IDF/v5.3.4/esp-idf/components/esp_psram D:/ESP_IDF/v5.3.4/esp-idf/components/esp_ringbuf D:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom D:/ESP_IDF/v5.3.4/esp-idf/components/esp_system D:/ESP_IDF/v5.3.4/esp-idf/components/esp_timer D:/ESP_IDF/v5.3.4/esp-idf/components/esp_vfs_console D:/ESP_IDF/v5.3.4/esp-idf/components/esp_wifi D:/ESP_IDF/v5.3.4/esp-idf/components/espcoredump C:/Users/<USER>/Desktop/i2c_oled/managed_components/espressif__esp_lcd_sh1107 C:/Users/<USER>/Desktop/i2c_oled/managed_components/espressif__esp_lvgl_port D:/ESP_IDF/v5.3.4/esp-idf/components/esptool_py D:/ESP_IDF/v5.3.4/esp-idf/components/fatfs D:/ESP_IDF/v5.3.4/esp-idf/components/freertos D:/ESP_IDF/v5.3.4/esp-idf/components/hal D:/ESP_IDF/v5.3.4/esp-idf/components/heap D:/ESP_IDF/v5.3.4/esp-idf/components/http_parser D:/ESP_IDF/v5.3.4/esp-idf/components/idf_test D:/ESP_IDF/v5.3.4/esp-idf/components/ieee802154 D:/ESP_IDF/v5.3.4/esp-idf/components/json D:/ESP_IDF/v5.3.4/esp-idf/components/log C:/Users/<USER>/Desktop/i2c_oled/managed_components/lvgl__lvgl D:/ESP_IDF/v5.3.4/esp-idf/components/lwip C:/Users/<USER>/Desktop/i2c_oled/main D:/ESP_IDF/v5.3.4/esp-idf/components/mbedtls D:/ESP_IDF/v5.3.4/esp-idf/components/mqtt D:/ESP_IDF/v5.3.4/esp-idf/components/newlib D:/ESP_IDF/v5.3.4/esp-idf/components/nvs_flash D:/ESP_IDF/v5.3.4/esp-idf/components/nvs_sec_provider D:/ESP_IDF/v5.3.4/esp-idf/components/openthread D:/ESP_IDF/v5.3.4/esp-idf/components/partition_table D:/ESP_IDF/v5.3.4/esp-idf/components/perfmon D:/ESP_IDF/v5.3.4/esp-idf/components/protobuf-c D:/ESP_IDF/v5.3.4/esp-idf/components/protocomm D:/ESP_IDF/v5.3.4/esp-idf/components/pthread D:/ESP_IDF/v5.3.4/esp-idf/components/sdmmc D:/ESP_IDF/v5.3.4/esp-idf/components/soc D:/ESP_IDF/v5.3.4/esp-idf/components/spi_flash D:/ESP_IDF/v5.3.4/esp-idf/components/spiffs D:/ESP_IDF/v5.3.4/esp-idf/components/tcp_transport D:/ESP_IDF/v5.3.4/esp-idf/components/touch_element D:/ESP_IDF/v5.3.4/esp-idf/components/ulp D:/ESP_IDF/v5.3.4/esp-idf/components/unity D:/ESP_IDF/v5.3.4/esp-idf/components/usb D:/ESP_IDF/v5.3.4/esp-idf/components/vfs D:/ESP_IDF/v5.3.4/esp-idf/components/wear_levelling D:/ESP_IDF/v5.3.4/esp-idf/components/wifi_provisioning D:/ESP_IDF/v5.3.4/esp-idf/components/wpa_supplicant D:/ESP_IDF/v5.3.4/esp-idf/components/xtensa
-- Configuring done (12.7s)
-- Generating done (2.1s)
-- Build files have been written to: C:/Users/<USER>/Desktop/i2c_oled/build
