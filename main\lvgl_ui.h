/*
 * LVGL UI Components Header File
 * 
 * This file contains the interface for LVGL UI components and functions.
 */

#ifndef LVGL_UI_H
#define LVGL_UI_H

#include "lvgl.h"

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief Create a scrolling text label
 * 
 * @param parent Parent object (usually screen)
 * @param text Text to display
 * @param width Width of the label
 * @return lv_obj_t* Created label object
 */
lv_obj_t* lvgl_ui_create_scroll_text(lv_obj_t *parent, const char *text, lv_coord_t width);

/**
 * @brief Create a simple text label
 * 
 * @param parent Parent object (usually screen)
 * @param text Text to display
 * @param x X position
 * @param y Y position
 * @return lv_obj_t* Created label object
 */
lv_obj_t* lvgl_ui_create_label(lv_obj_t *parent, const char *text, lv_coord_t x, lv_coord_t y);

/**
 * @brief Create a progress bar
 * 
 * @param parent Parent object (usually screen)
 * @param width Width of the progress bar
 * @param height Height of the progress bar
 * @param x X position
 * @param y Y position
 * @return lv_obj_t* Created progress bar object
 */
lv_obj_t* lvgl_ui_create_progress_bar(lv_obj_t *parent, lv_coord_t width, lv_coord_t height, lv_coord_t x, lv_coord_t y);

/**
 * @brief Update progress bar value
 * 
 * @param bar Progress bar object
 * @param value Value (0-100)
 * @param anim Enable animation
 */
void lvgl_ui_set_progress_value(lv_obj_t *bar, int32_t value, bool anim);

/**
 * @brief Create a simple button
 * 
 * @param parent Parent object (usually screen)
 * @param text Button text
 * @param width Button width
 * @param height Button height
 * @param x X position
 * @param y Y position
 * @return lv_obj_t* Created button object
 */
lv_obj_t* lvgl_ui_create_button(lv_obj_t *parent, const char *text, lv_coord_t width, lv_coord_t height, lv_coord_t x, lv_coord_t y);

/**
 * @brief Create a multi-line text area
 * 
 * @param parent Parent object (usually screen)
 * @param text Initial text
 * @param width Width of the text area
 * @param height Height of the text area
 * @param x X position
 * @param y Y position
 * @return lv_obj_t* Created text area object
 */
lv_obj_t* lvgl_ui_create_text_area(lv_obj_t *parent, const char *text, lv_coord_t width, lv_coord_t height, lv_coord_t x, lv_coord_t y);

/**
 * @brief Update text of a label
 * 
 * @param label Label object
 * @param text New text
 */
void lvgl_ui_update_label_text(lv_obj_t *label, const char *text);

/**
 * @brief Clear all objects from screen
 * 
 * @param scr Screen object
 */
void lvgl_ui_clear_screen(lv_obj_t *scr);

/**
 * @brief Create a demo UI with scrolling text (original example)
 * 
 * @param disp Display object
 */
void lvgl_ui_demo_scroll_text(lv_disp_t *disp);

/**
 * @brief Create a demo UI with multiple elements
 * 
 * @param disp Display object
 */
void lvgl_ui_demo_multi_elements(lv_disp_t *disp);

/**
 * @brief Create a demo UI with progress bar
 * 
 * @param disp Display object
 */
void lvgl_ui_demo_progress(lv_disp_t *disp);

/**
 * @brief Create a demo UI with system information
 * 
 * @param disp Display object
 */
void lvgl_ui_demo_system_info(lv_disp_t *disp);

#ifdef __cplusplus
}
#endif

#endif // LVGL_UI_H
