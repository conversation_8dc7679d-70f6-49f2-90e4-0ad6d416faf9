﻿# I2C OLED 可移植工程

这是一个重构后的可移植I2C OLED显示项目，具有清晰的模块化结构。

## 项目结构

`
i2c_oled/
 main/                    # 主程序目录
    main.c              # 主程序文件（仅包含应用逻辑）
    CMakeLists.txt      # 主程序构建配置
    Kconfig.projbuild   # 项目配置选项
    idf_component.yml   # 组件依赖配置
 components/              # 自定义组件目录
    oled/               # OLED驱动组件
       oled.h          # OLED驱动头文件
       oled.c          # OLED驱动实现（I2C配置和显示功能）
       CMakeLists.txt  # OLED组件构建配置
    lvgl_ui/            # LVGL UI组件
        lvgl_ui.h       # LVGL UI组件头文件
        lvgl_ui.c       # LVGL UI组件实现
        CMakeLists.txt  # LVGL UI组件构建配置
 managed_components/      # 外部依赖组件
 CMakeLists.txt          # 项目根构建配置
 README.md               # 本说明文件
`

## 使用方法

### 修改显示内容

在 main/main.c 文件中的 display_content() 函数中选择显示模式。

### 修改硬件配置

在 main/main.c 文件中的 init_display() 函数中修改引脚配置。

## 编译和运行

1. 确保ESP-IDF环境已正确配置
2. 配置项目：idf.py menuconfig
3. 编译项目：idf.py build
4. 烧录和监控：idf.py flash monitor
