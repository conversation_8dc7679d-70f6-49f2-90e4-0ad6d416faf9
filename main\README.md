# I2C OLED 可移植工程

这是一个重构后的可移植I2C OLED显示项目，具有清晰的模块化结构。

## 项目结构

```
main/
├── main.c          # 主程序文件，包含应用逻辑
├── oled.h          # OLED驱动头文件
├── oled.c          # OLED驱动实现，包含I2C配置和显示功能
├── lvgl_ui.h       # LVGL UI组件头文件
├── lvgl_ui.c       # LVGL UI组件实现
├── CMakeLists.txt  # 构建配置文件
└── README.md       # 本说明文件
```

## 模块说明

### 1. OLED模块 (oled.h/oled.c)
- 负责OLED显示器的I2C配置
- 提供OLED初始化、去初始化功能
- 包含显示控制函数（开关、亮度等）
- 提供LVGL显示对象接口

### 2. LVGL UI模块 (lvgl_ui.h/lvgl_ui.c)
- 提供各种UI组件创建函数
- 包含多种演示UI界面
- 提供文本、进度条、按钮等常用组件

### 3. 主程序模块 (main.c)
- 包含应用程序入口点
- 负责显示内容的调用
- 提供简单的配置接口

## 使用方法

### 1. 修改显示内容

在 `main.c` 文件中的 `display_content()` 函数中修改显示内容：

```c
static void display_content(void)
{
    lv_disp_t *disp = oled_get_display(oled_handle);
    
    if (lvgl_port_lock(0)) {
        // 选择一种显示模式：
        
        // 选项1：滚动文本演示
        lvgl_ui_demo_scroll_text(disp);
        
        // 选项2：多元素演示
        // lvgl_ui_demo_multi_elements(disp);
        
        // 选项3：进度条演示
        // lvgl_ui_demo_progress(disp);
        
        // 选项4：系统信息演示
        // lvgl_ui_demo_system_info(disp);
        
        // 选项5：自定义UI
        /*
        lv_obj_t *scr = lv_disp_get_scr_act(disp);
        lvgl_ui_clear_screen(scr);
        lv_obj_t *my_label = lvgl_ui_create_label(scr, "我的文本", 0, 0);
        lv_obj_align(my_label, LV_ALIGN_CENTER, 0, 0);
        */
        
        lvgl_port_unlock();
    }
}
```

### 2. 修改硬件配置

在 `main.c` 文件中的 `init_display()` 函数中修改引脚配置：

```c
static esp_err_t init_display(void)
{
    oled_config_t config = oled_get_default_config();
    
    // 根据需要修改这些配置
    config.sda_pin = 3;          // SDA引脚
    config.scl_pin = 4;          // SCL引脚
    config.rst_pin = -1;         // 复位引脚（-1表示不使用）
    config.i2c_addr = 0x3C;      // I2C地址
    config.pixel_clock_hz = 400000; // I2C时钟频率
    
    return oled_init(&config, &oled_handle);
}
```

### 3. 添加自定义UI组件

可以在 `lvgl_ui.c` 中添加新的UI组件函数，然后在 `lvgl_ui.h` 中声明接口。

## 默认配置

- SDA引脚：GPIO 3
- SCL引脚：GPIO 4
- I2C地址：0x3C
- I2C时钟：400kHz
- 显示分辨率：根据配置的控制器自动设置

## 支持的显示控制器

- SSD1306 (128x64 或 128x32)
- SH1107 (64x128)

## 编译和运行

1. 确保ESP-IDF环境已正确配置
2. 在项目根目录运行：
   ```bash
   idf.py build
   idf.py flash monitor
   ```

## 移植说明

要将此项目移植到其他平台：

1. 修改 `oled.c` 中的I2C驱动部分
2. 修改 `lvgl_ui.c` 中的系统相关函数
3. 根据目标平台调整 `CMakeLists.txt`
4. 保持头文件接口不变，确保兼容性

## 注意事项

- 在调用LVGL API时需要使用 `lvgl_port_lock()` 和 `lvgl_port_unlock()`
- 修改显示内容时建议先清屏：`lvgl_ui_clear_screen(scr)`
- 自定义UI时注意屏幕尺寸限制
