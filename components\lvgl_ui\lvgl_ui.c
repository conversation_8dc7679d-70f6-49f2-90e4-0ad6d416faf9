/*
 * LVGL UI Components Implementation
 * 
 * This file contains the implementation of LVGL UI components and functions.
 */

#include "lvgl_ui.h"
#include "esp_log.h"
#include "esp_system.h"
#include "esp_chip_info.h"
#include "esp_flash.h"
#include <stdio.h>

static const char *TAG = "lvgl_ui";

lv_obj_t* lvgl_ui_create_scroll_text(lv_obj_t *parent, const char *text, lv_coord_t width)
{
    lv_obj_t *label = lv_label_create(parent);
    lv_label_set_long_mode(label, LV_LABEL_LONG_SCROLL_CIRCULAR);
    lv_label_set_text(label, text);
    lv_obj_set_width(label, width);
    lv_obj_align(label, LV_ALIGN_TOP_MID, 0, 0);
    return label;
}

lv_obj_t* lvgl_ui_create_label(lv_obj_t *parent, const char *text, lv_coord_t x, lv_coord_t y)
{
    lv_obj_t *label = lv_label_create(parent);
    lv_label_set_text(label, text);
    lv_obj_set_pos(label, x, y);
    return label;
}

lv_obj_t* lvgl_ui_create_progress_bar(lv_obj_t *parent, lv_coord_t width, lv_coord_t height, lv_coord_t x, lv_coord_t y)
{
    lv_obj_t *bar = lv_bar_create(parent);
    lv_obj_set_size(bar, width, height);
    lv_obj_set_pos(bar, x, y);
    lv_bar_set_range(bar, 0, 100);
    return bar;
}

void lvgl_ui_set_progress_value(lv_obj_t *bar, int32_t value, bool anim)
{
    if (bar == NULL) return;
    
    if (anim) {
        lv_bar_set_value(bar, value, LV_ANIM_ON);
    } else {
        lv_bar_set_value(bar, value, LV_ANIM_OFF);
    }
}

lv_obj_t* lvgl_ui_create_button(lv_obj_t *parent, const char *text, lv_coord_t width, lv_coord_t height, lv_coord_t x, lv_coord_t y)
{
    lv_obj_t *btn = lv_btn_create(parent);
    lv_obj_set_size(btn, width, height);
    lv_obj_set_pos(btn, x, y);
    
    lv_obj_t *label = lv_label_create(btn);
    lv_label_set_text(label, text);
    lv_obj_center(label);
    
    return btn;
}

lv_obj_t* lvgl_ui_create_text_area(lv_obj_t *parent, const char *text, lv_coord_t width, lv_coord_t height, lv_coord_t x, lv_coord_t y)
{
    lv_obj_t *ta = lv_textarea_create(parent);
    lv_obj_set_size(ta, width, height);
    lv_obj_set_pos(ta, x, y);
    lv_textarea_set_text(ta, text);
    return ta;
}

void lvgl_ui_update_label_text(lv_obj_t *label, const char *text)
{
    if (label == NULL || text == NULL) return;
    lv_label_set_text(label, text);
}

void lvgl_ui_clear_screen(lv_obj_t *scr)
{
    if (scr == NULL) return;
    lv_obj_clean(scr);
}

void lvgl_ui_demo_scroll_text(lv_disp_t *disp)
{
    lv_obj_t *scr = lv_disp_get_scr_act(disp);
    lv_obj_t *label = lvgl_ui_create_scroll_text(scr, "Hello Espressif, Hello LVGL.", disp->driver->hor_res);
    ESP_LOGI(TAG, "Created scrolling text demo");
}

void lvgl_ui_demo_multi_elements(lv_disp_t *disp)
{
    lv_obj_t *scr = lv_disp_get_scr_act(disp);
    
    // Clear screen first
    lvgl_ui_clear_screen(scr);
    
    // Create title
    lv_obj_t *title = lvgl_ui_create_label(scr, "Multi Demo", 0, 0);
    lv_obj_align(title, LV_ALIGN_TOP_MID, 0, 0);
    
    // Create progress bar
    lv_obj_t *bar = lvgl_ui_create_progress_bar(scr, 80, 8, 0, 20);
    lv_obj_align(bar, LV_ALIGN_TOP_MID, 0, 20);
    lvgl_ui_set_progress_value(bar, 75, true);
    
    // Create status label
    lv_obj_t *status = lvgl_ui_create_label(scr, "Status: OK", 0, 40);
    lv_obj_align(status, LV_ALIGN_TOP_MID, 0, 40);
    
    ESP_LOGI(TAG, "Created multi-element demo");
}

void lvgl_ui_demo_progress(lv_disp_t *disp)
{
    lv_obj_t *scr = lv_disp_get_scr_act(disp);
    
    // Clear screen first
    lvgl_ui_clear_screen(scr);
    
    // Create title
    lv_obj_t *title = lvgl_ui_create_label(scr, "Progress", 0, 0);
    lv_obj_align(title, LV_ALIGN_TOP_MID, 0, 0);
    
    // Create progress bar
    lv_obj_t *bar = lvgl_ui_create_progress_bar(scr, 100, 10, 0, 20);
    lv_obj_align(bar, LV_ALIGN_TOP_MID, 0, 20);
    
    // Animate progress from 0 to 100
    for (int i = 0; i <= 100; i += 10) {
        lvgl_ui_set_progress_value(bar, i, true);
        vTaskDelay(pdMS_TO_TICKS(100));
    }
    
    // Create completion label
    lv_obj_t *complete = lvgl_ui_create_label(scr, "Complete!", 0, 40);
    lv_obj_align(complete, LV_ALIGN_TOP_MID, 0, 40);
    
    ESP_LOGI(TAG, "Created progress demo");
}

void lvgl_ui_demo_system_info(lv_disp_t *disp)
{
    lv_obj_t *scr = lv_disp_get_scr_act(disp);
    
    // Clear screen first
    lvgl_ui_clear_screen(scr);
    
    // Get system information
    esp_chip_info_t chip_info;
    esp_chip_info(&chip_info);
    
    uint32_t flash_size;
    esp_flash_get_size(NULL, &flash_size);
    
    // Create system info text
    char info_text[256];
    snprintf(info_text, sizeof(info_text), 
             "ESP32\n"
             "Cores: %d\n"
             "Flash: %luMB\n"
             "Free: %lu",
             chip_info.cores,
             flash_size / (1024 * 1024),
             esp_get_free_heap_size());
    
    // Create scrolling text with system info
    lv_obj_t *info_label = lvgl_ui_create_scroll_text(scr, info_text, disp->driver->hor_res);
    
    ESP_LOGI(TAG, "Created system info demo");
}
