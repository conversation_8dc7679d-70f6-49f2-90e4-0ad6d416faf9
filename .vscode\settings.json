{"C_Cpp.intelliSenseEngine": "default", "idf.espIdfPathWin": "d:\\ESP_IDF\\v5.3.4\\esp-idf", "idf.toolsPathWin": "d:\\ESP_IDF\\idf_tools", "idf.pythonInstallPath": "d:\\ESP_IDF\\idf_tools\\tools\\idf-python\\3.11.2\\python.exe", "idf.flashType": "UART", "idf.portWin": "COM6", "idf.openOcdConfigs": ["interface/ftdi/esp_ftdi.cfg", "target/esp32s3.cfg"], "idf.customExtraVars": {"IDF_TARGET": "esp32s3"}, "clangd.path": "d:\\ESP_IDF\\idf_tools\\tools\\esp-clang\\16.0.1-fe4f10a809\\esp-clang\\bin\\clangd.exe", "clangd.arguments": ["--background-index", "--query-driver=d:\\ESP_IDF\\idf_tools\\tools\\xtensa-esp-elf\\esp-13.2.0_20240530\\xtensa-esp-elf\\bin\\xtensa-esp32s3-elf-gcc.exe", "--compile-commands-dir=c:\\Users\\<USER>\\Desktop\\i2c_oled\\build"]}