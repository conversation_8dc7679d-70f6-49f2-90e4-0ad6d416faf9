/*
 * OLED Display Driver Implementation
 * 
 * This file contains the implementation of OLED display operations
 * including I2C configuration and display functions.
 */

#include "oled.h"
#include "esp_log.h"
#include "esp_lvgl_port.h"
#include <stdlib.h>
#include <string.h>

#if CONFIG_EXAMPLE_LCD_CONTROLLER_SH1107
#include "esp_lcd_sh1107.h"
#else
#include "esp_lcd_panel_vendor.h"
#endif

static const char *TAG = "oled";

// Default configuration values
#define DEFAULT_SDA_PIN           3
#define DEFAULT_SCL_PIN           4
#define DEFAULT_RST_PIN           -1
#define DEFAULT_I2C_ADDR          0x3C
#define DEFAULT_PIXEL_CLOCK_HZ    (400 * 1000)
#define DEFAULT_I2C_PORT          0

// Default resolution based on controller
#if CONFIG_EXAMPLE_LCD_CONTROLLER_SSD1306
#define DEFAULT_H_RES             128
#define DEFAULT_V_RES             CONFIG_EXAMPLE_SSD1306_HEIGHT
#elif CONFIG_EXAMPLE_LCD_CONTROLLER_SH1107
#define DEFAULT_H_RES             64
#define DEFAULT_V_RES             128
#else
#define DEFAULT_H_RES             128
#define DEFAULT_V_RES             64
#endif

// LCD command and parameter bits
#define LCD_CMD_BITS              8
#define LCD_PARAM_BITS            8

oled_config_t oled_get_default_config(void)
{
    oled_config_t config = {
        .sda_pin = DEFAULT_SDA_PIN,
        .scl_pin = DEFAULT_SCL_PIN,
        .rst_pin = DEFAULT_RST_PIN,
        .i2c_addr = DEFAULT_I2C_ADDR,
        .pixel_clock_hz = DEFAULT_PIXEL_CLOCK_HZ,
        .h_res = DEFAULT_H_RES,
        .v_res = DEFAULT_V_RES,
        .i2c_port = DEFAULT_I2C_PORT,
    };
    return config;
}

esp_err_t oled_init(const oled_config_t *config, oled_handle_t **handle)
{
    if (config == NULL || handle == NULL) {
        return ESP_ERR_INVALID_ARG;
    }

    // Allocate handle
    *handle = (oled_handle_t*)malloc(sizeof(oled_handle_t));
    if (*handle == NULL) {
        ESP_LOGE(TAG, "Failed to allocate memory for OLED handle");
        return ESP_ERR_NO_MEM;
    }

    // Copy configuration
    memcpy(&(*handle)->config, config, sizeof(oled_config_t));

    ESP_LOGI(TAG, "Initialize I2C bus");
    i2c_master_bus_config_t bus_config = {
        .clk_source = I2C_CLK_SRC_DEFAULT,
        .glitch_ignore_cnt = 7,
        .i2c_port = config->i2c_port,
        .sda_io_num = config->sda_pin,
        .scl_io_num = config->scl_pin,
        .flags.enable_internal_pullup = true,
    };
    
    esp_err_t ret = i2c_new_master_bus(&bus_config, &(*handle)->i2c_bus);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to create I2C master bus");
        free(*handle);
        *handle = NULL;
        return ret;
    }

    ESP_LOGI(TAG, "Install panel IO");
    esp_lcd_panel_io_i2c_config_t io_config = {
        .dev_addr = config->i2c_addr,
        .scl_speed_hz = config->pixel_clock_hz,
        .control_phase_bytes = 1,
        .lcd_cmd_bits = LCD_CMD_BITS,
        .lcd_param_bits = LCD_PARAM_BITS,
#if CONFIG_EXAMPLE_LCD_CONTROLLER_SSD1306
        .dc_bit_offset = 6,
#elif CONFIG_EXAMPLE_LCD_CONTROLLER_SH1107
        .dc_bit_offset = 0,
        .flags = {
            .disable_control_phase = 1,
        }
#endif
    };
    
    ret = esp_lcd_new_panel_io_i2c((*handle)->i2c_bus, &io_config, &(*handle)->io_handle);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to create panel IO");
        i2c_del_master_bus((*handle)->i2c_bus);
        free(*handle);
        *handle = NULL;
        return ret;
    }

    ESP_LOGI(TAG, "Install LCD panel driver");
    esp_lcd_panel_dev_config_t panel_config = {
        .bits_per_pixel = 1,
        .reset_gpio_num = config->rst_pin,
    };

#if CONFIG_EXAMPLE_LCD_CONTROLLER_SSD1306
    esp_lcd_panel_ssd1306_config_t ssd1306_config = {
        .height = config->v_res,
    };
    panel_config.vendor_config = &ssd1306_config;
    ret = esp_lcd_new_panel_ssd1306((*handle)->io_handle, &panel_config, &(*handle)->panel_handle);
#elif CONFIG_EXAMPLE_LCD_CONTROLLER_SH1107
    ret = esp_lcd_new_panel_sh1107((*handle)->io_handle, &panel_config, &(*handle)->panel_handle);
#endif

    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to create LCD panel");
        esp_lcd_panel_io_del((*handle)->io_handle);
        i2c_del_master_bus((*handle)->i2c_bus);
        free(*handle);
        *handle = NULL;
        return ret;
    }

    // Reset and initialize panel
    ESP_ERROR_CHECK(esp_lcd_panel_reset((*handle)->panel_handle));
    ESP_ERROR_CHECK(esp_lcd_panel_init((*handle)->panel_handle));
    ESP_ERROR_CHECK(esp_lcd_panel_disp_on_off((*handle)->panel_handle, true));

#if CONFIG_EXAMPLE_LCD_CONTROLLER_SH1107
    ESP_ERROR_CHECK(esp_lcd_panel_invert_color((*handle)->panel_handle, true));
#endif

    ESP_LOGI(TAG, "Initialize LVGL");
    const lvgl_port_cfg_t lvgl_cfg = ESP_LVGL_PORT_INIT_CONFIG();
    lvgl_port_init(&lvgl_cfg);

    const lvgl_port_display_cfg_t disp_cfg = {
        .io_handle = (*handle)->io_handle,
        .panel_handle = (*handle)->panel_handle,
        .buffer_size = config->h_res * config->v_res,
        .double_buffer = true,
        .hres = config->h_res,
        .vres = config->v_res,
        .monochrome = true,
        .rotation = {
            .swap_xy = false,
            .mirror_x = false,
            .mirror_y = false,
        }
    };
    
    (*handle)->disp = lvgl_port_add_disp(&disp_cfg);
    if ((*handle)->disp == NULL) {
        ESP_LOGE(TAG, "Failed to add LVGL display");
        esp_lcd_panel_del((*handle)->panel_handle);
        esp_lcd_panel_io_del((*handle)->io_handle);
        i2c_del_master_bus((*handle)->i2c_bus);
        free(*handle);
        *handle = NULL;
        return ESP_FAIL;
    }

    // Set rotation
    lv_disp_set_rotation((*handle)->disp, LV_DISP_ROT_NONE);

    ESP_LOGI(TAG, "OLED initialization completed successfully");
    return ESP_OK;
}

esp_err_t oled_deinit(oled_handle_t *handle)
{
    if (handle == NULL) {
        return ESP_ERR_INVALID_ARG;
    }

    // Clean up LVGL display
    if (handle->disp) {
        lvgl_port_remove_disp(handle->disp);
    }

    // Clean up LCD panel
    if (handle->panel_handle) {
        esp_lcd_panel_del(handle->panel_handle);
    }

    // Clean up panel IO
    if (handle->io_handle) {
        esp_lcd_panel_io_del(handle->io_handle);
    }

    // Clean up I2C bus
    if (handle->i2c_bus) {
        i2c_del_master_bus(handle->i2c_bus);
    }

    // Free handle
    free(handle);
    
    ESP_LOGI(TAG, "OLED deinitialized");
    return ESP_OK;
}

lv_disp_t* oled_get_display(oled_handle_t *handle)
{
    if (handle == NULL) {
        return NULL;
    }
    return handle->disp;
}

esp_err_t oled_display_on_off(oled_handle_t *handle, bool on)
{
    if (handle == NULL || handle->panel_handle == NULL) {
        return ESP_ERR_INVALID_ARG;
    }
    return esp_lcd_panel_disp_on_off(handle->panel_handle, on);
}

esp_err_t oled_set_brightness(oled_handle_t *handle, uint8_t brightness)
{
    if (handle == NULL || handle->panel_handle == NULL) {
        return ESP_ERR_INVALID_ARG;
    }
    // Note: Not all OLED controllers support brightness control
    // This is a placeholder implementation
    ESP_LOGW(TAG, "Brightness control may not be supported by this OLED controller");
    return ESP_OK;
}

esp_err_t oled_clear(oled_handle_t *handle)
{
    if (handle == NULL || handle->disp == NULL) {
        return ESP_ERR_INVALID_ARG;
    }
    
    if (lvgl_port_lock(0)) {
        lv_obj_clean(lv_scr_act());
        lvgl_port_unlock();
    }
    
    return ESP_OK;
}
