{"COMPONENT_KCONFIGS": "D:/ESP_IDF/v5.3.4/esp-idf/components/efuse/Kconfig;D:/ESP_IDF/v5.3.4/esp-idf/components/esp_common/Kconfig;D:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/Kconfig;D:/ESP_IDF/v5.3.4/esp-idf/components/esp_system/Kconfig;D:/ESP_IDF/v5.3.4/esp-idf/components/freertos/Kconfig;D:/ESP_IDF/v5.3.4/esp-idf/components/hal/Kconfig;D:/ESP_IDF/v5.3.4/esp-idf/components/log/Kconfig;D:/ESP_IDF/v5.3.4/esp-idf/components/newlib/Kconfig;D:/ESP_IDF/v5.3.4/esp-idf/components/soc/Kconfig;D:/ESP_IDF/v5.3.4/esp-idf/components/spi_flash/Kconfig", "COMPONENT_KCONFIGS_PROJBUILD": "D:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/Kconfig.projbuild;D:/ESP_IDF/v5.3.4/esp-idf/components/esp_app_format/Kconfig.projbuild;D:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/Kconfig.projbuild;D:/ESP_IDF/v5.3.4/esp-idf/components/esptool_py/Kconfig.projbuild;D:/ESP_IDF/v5.3.4/esp-idf/components/partition_table/Kconfig.projbuild", "COMPONENT_SDKCONFIG_RENAMES": "D:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/sdkconfig.rename;D:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/sdkconfig.rename;D:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/sdkconfig.rename.esp32s3;D:/ESP_IDF/v5.3.4/esp-idf/components/esp_system/sdkconfig.rename;D:/ESP_IDF/v5.3.4/esp-idf/components/esp_system/sdkconfig.rename.esp32s3;D:/ESP_IDF/v5.3.4/esp-idf/components/esptool_py/sdkconfig.rename;D:/ESP_IDF/v5.3.4/esp-idf/components/freertos/sdkconfig.rename;D:/ESP_IDF/v5.3.4/esp-idf/components/hal/sdkconfig.rename;D:/ESP_IDF/v5.3.4/esp-idf/components/newlib/sdkconfig.rename.esp32s3;D:/ESP_IDF/v5.3.4/esp-idf/components/spi_flash/sdkconfig.rename", "IDF_TARGET": "esp32s3", "IDF_TOOLCHAIN": "gcc", "IDF_VERSION": "5.3.4", "IDF_ENV_FPGA": "", "IDF_PATH": "D:/ESP_IDF/v5.3.4/esp-idf", "COMPONENT_KCONFIGS_SOURCE_FILE": "C:/Users/<USER>/Desktop/i2c_oled/build/bootloader/kconfigs.in", "COMPONENT_KCONFIGS_PROJBUILD_SOURCE_FILE": "C:/Users/<USER>/Desktop/i2c_oled/build/bootloader/kconfigs_projbuild.in"}