/*
 * OLED Display Driver Header File
 * 
 * This file contains the interface for OLED display operations
 * including I2C configuration and display functions.
 */

#ifndef OLED_H
#define OLED_H

#include "esp_lcd_panel_io.h"
#include "esp_lcd_panel_ops.h"
#include "esp_err.h"
#include "driver/i2c_master.h"
#include "lvgl.h"

#ifdef __cplusplus
extern "C" {
#endif

// OLED Configuration Structure
typedef struct {
    int sda_pin;                    // SDA pin number
    int scl_pin;                    // SCL pin number  
    int rst_pin;                    // Reset pin number (-1 if not used)
    uint8_t i2c_addr;              // I2C device address
    uint32_t pixel_clock_hz;       // I2C clock frequency
    uint16_t h_res;                // Horizontal resolution
    uint16_t v_res;                // Vertical resolution
    int i2c_port;                  // I2C port number
} oled_config_t;

// OLED Handle Structure
typedef struct {
    i2c_master_bus_handle_t i2c_bus;
    esp_lcd_panel_io_handle_t io_handle;
    esp_lcd_panel_handle_t panel_handle;
    lv_disp_t *disp;
    oled_config_t config;
} oled_handle_t;

/**
 * @brief Get default OLED configuration
 * 
 * @return oled_config_t Default configuration structure
 */
oled_config_t oled_get_default_config(void);

/**
 * @brief Initialize OLED display
 * 
 * @param config Pointer to OLED configuration
 * @param handle Pointer to OLED handle (will be allocated)
 * @return esp_err_t ESP_OK on success
 */
esp_err_t oled_init(const oled_config_t *config, oled_handle_t **handle);

/**
 * @brief Deinitialize OLED display
 * 
 * @param handle Pointer to OLED handle
 * @return esp_err_t ESP_OK on success
 */
esp_err_t oled_deinit(oled_handle_t *handle);

/**
 * @brief Get LVGL display object
 * 
 * @param handle Pointer to OLED handle
 * @return lv_disp_t* LVGL display object
 */
lv_disp_t* oled_get_display(oled_handle_t *handle);

/**
 * @brief Turn display on/off
 * 
 * @param handle Pointer to OLED handle
 * @param on true to turn on, false to turn off
 * @return esp_err_t ESP_OK on success
 */
esp_err_t oled_display_on_off(oled_handle_t *handle, bool on);

/**
 * @brief Set display brightness (if supported)
 * 
 * @param handle Pointer to OLED handle
 * @param brightness Brightness level (0-255)
 * @return esp_err_t ESP_OK on success
 */
esp_err_t oled_set_brightness(oled_handle_t *handle, uint8_t brightness);

/**
 * @brief Clear display
 * 
 * @param handle Pointer to OLED handle
 * @return esp_err_t ESP_OK on success
 */
esp_err_t oled_clear(oled_handle_t *handle);

#ifdef __cplusplus
}
#endif

#endif // OLED_H
