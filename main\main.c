/*
 * Main Application File
 * 
 * This file contains the main application logic.
 * To change display content, modify the display_content() function.
 */

#include <stdio.h>
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "esp_log.h"
#include "esp_lvgl_port.h"
#include "oled.h"
#include "lvgl_ui.h"

static const char *TAG = "main";

// OLED handle
static oled_handle_t *oled_handle = NULL;

/**
 * @brief Display content function
 * 
 * Modify this function to change what is displayed on the OLED.
 * You can call different lvgl_ui_demo_* functions or create your own UI.
 */
static void display_content(void)
{
    lv_disp_t *disp = oled_get_display(oled_handle);
    if (disp == NULL) {
        ESP_LOGE(TAG, "Failed to get display object");
        return;
    }

    // Lock the mutex due to the LVGL APIs are not thread-safe
    if (lvgl_port_lock(0)) {
        
        // ========================================
        // MODIFY THIS SECTION TO CHANGE DISPLAY CONTENT
        // ========================================
        
        // Option 1: Original scrolling text demo
        lvgl_ui_demo_scroll_text(disp);
        
        // Option 2: Multi-element demo (uncomment to use)
        // lvgl_ui_demo_multi_elements(disp);
        
        // Option 3: Progress bar demo (uncomment to use)
        // lvgl_ui_demo_progress(disp);
        
        // Option 4: System information demo (uncomment to use)
        // lvgl_ui_demo_system_info(disp);
        
        // Option 5: Custom display content (example)
        /*
        lv_obj_t *scr = lv_disp_get_scr_act(disp);
        lvgl_ui_clear_screen(scr);
        
        // Create your custom UI elements here
        lv_obj_t *my_label = lvgl_ui_create_label(scr, "My Custom Text", 0, 0);
        lv_obj_align(my_label, LV_ALIGN_CENTER, 0, 0);
        
        // Add more UI elements as needed
        lv_obj_t *my_progress = lvgl_ui_create_progress_bar(scr, 80, 8, 0, 20);
        lv_obj_align(my_progress, LV_ALIGN_CENTER, 0, 20);
        lvgl_ui_set_progress_value(my_progress, 50, true);
        */
        
        // ========================================
        // END OF DISPLAY CONTENT SECTION
        // ========================================
        
        // Release the mutex
        lvgl_port_unlock();
    }
}

/**
 * @brief Initialize OLED display with default or custom configuration
 */
static esp_err_t init_display(void)
{
    // Get default configuration
    oled_config_t config = oled_get_default_config();
    
    // ========================================
    // MODIFY THESE VALUES IF NEEDED
    // ========================================
    
    // Uncomment and modify these lines to customize pin configuration
    // config.sda_pin = 3;          // SDA pin
    // config.scl_pin = 4;          // SCL pin
    // config.rst_pin = -1;         // Reset pin (-1 if not used)
    // config.i2c_addr = 0x3C;      // I2C address
    // config.pixel_clock_hz = 400000; // I2C clock frequency
    
    // ========================================
    // END OF CONFIGURATION SECTION
    // ========================================
    
    // Initialize OLED
    esp_err_t ret = oled_init(&config, &oled_handle);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to initialize OLED: %s", esp_err_to_name(ret));
        return ret;
    }
    
    ESP_LOGI(TAG, "OLED initialized successfully");
    return ESP_OK;
}

/**
 * @brief Main application entry point
 */
void app_main(void)
{
    ESP_LOGI(TAG, "Starting I2C OLED example");
    
    // Initialize display
    esp_err_t ret = init_display();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Display initialization failed");
        return;
    }
    
    // Display content
    ESP_LOGI(TAG, "Displaying content on OLED");
    display_content();
    
    // Main loop (optional - add your application logic here)
    while (1) {
        // You can add periodic updates here if needed
        // For example, update system information every 5 seconds:
        /*
        vTaskDelay(pdMS_TO_TICKS(5000));
        if (lvgl_port_lock(0)) {
            lvgl_ui_demo_system_info(oled_get_display(oled_handle));
            lvgl_port_unlock();
        }
        */
        
        // For now, just delay to prevent busy waiting
        vTaskDelay(pdMS_TO_TICKS(1000));
    }
    
    // Cleanup (this code will never be reached in this example)
    oled_deinit(oled_handle);
}
