[{"directory": "C:/Users/<USER>/Desktop/i2c_oled/build/bootloader", "command": "D:\\ESP_IDF\\idf_tools\\tools\\xtensa-esp-elf\\esp-13.2.0_20240530\\xtensa-esp-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/deprecated_include -IC:/Users/<USER>/Desktop/i2c_oled/build/bootloader/config -ID:/ESP_IDF/v5.3.4/esp-idf/components/log/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_common/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/dma/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/ldo/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/newlib/platform_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/subproject/components/micro-ecc -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/subproject/components/micro-ecc/micro-ecc -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/platform_port/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/spi_flash/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_bootloader_format/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_app_format/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader_support/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader_support/bootloader_flash/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader_support/private_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/efuse/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/efuse/esp32s3/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -o CMakeFiles\\bootloader.elf.dir\\project_elf_src_esp32s3.c.obj -c C:\\Users\\<USER>\\Desktop\\i2c_oled\\build\\bootloader\\project_elf_src_esp32s3.c", "file": "C:\\Users\\<USER>\\Desktop\\i2c_oled\\build\\bootloader\\project_elf_src_esp32s3.c", "output": "CMakeFiles\\bootloader.elf.dir\\project_elf_src_esp32s3.c.obj"}, {"directory": "C:/Users/<USER>/Desktop/i2c_oled/build/bootloader", "command": "D:\\ESP_IDF\\idf_tools\\tools\\xtensa-esp-elf\\esp-13.2.0_20240530\\xtensa-esp-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.4\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/Desktop/i2c_oled/build/bootloader/config -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/deprecated_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/log/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_common/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/dma/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/ldo/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/newlib/platform_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\xtensa\\CMakeFiles\\__idf_xtensa.dir\\eri.c.obj -c D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\xtensa\\eri.c", "file": "D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\xtensa\\eri.c", "output": "esp-idf\\xtensa\\CMakeFiles\\__idf_xtensa.dir\\eri.c.obj"}, {"directory": "C:/Users/<USER>/Desktop/i2c_oled/build/bootloader", "command": "D:\\ESP_IDF\\idf_tools\\tools\\xtensa-esp-elf\\esp-13.2.0_20240530\\xtensa-esp-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.4\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/Desktop/i2c_oled/build/bootloader/config -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/deprecated_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/log/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_common/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/dma/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/ldo/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/newlib/platform_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\xtensa\\CMakeFiles\\__idf_xtensa.dir\\xt_trax.c.obj -c D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\xtensa\\xt_trax.c", "file": "D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\xtensa\\xt_trax.c", "output": "esp-idf\\xtensa\\CMakeFiles\\__idf_xtensa.dir\\xt_trax.c.obj"}, {"directory": "C:/Users/<USER>/Desktop/i2c_oled/build/bootloader", "command": "D:\\ESP_IDF\\idf_tools\\tools\\xtensa-esp-elf\\esp-13.2.0_20240530\\xtensa-esp-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.4\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/Desktop/i2c_oled/build/bootloader/config -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/log/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_common/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/dma/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/ldo/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/newlib/platform_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/deprecated_include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\lldesc.c.obj -c D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\soc\\lldesc.c", "file": "D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\soc\\lldesc.c", "output": "esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\lldesc.c.obj"}, {"directory": "C:/Users/<USER>/Desktop/i2c_oled/build/bootloader", "command": "D:\\ESP_IDF\\idf_tools\\tools\\xtensa-esp-elf\\esp-13.2.0_20240530\\xtensa-esp-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.4\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/Desktop/i2c_oled/build/bootloader/config -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/log/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_common/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/dma/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/ldo/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/newlib/platform_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/deprecated_include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\dport_access_common.c.obj -c D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\soc\\dport_access_common.c", "file": "D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\soc\\dport_access_common.c", "output": "esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\dport_access_common.c.obj"}, {"directory": "C:/Users/<USER>/Desktop/i2c_oled/build/bootloader", "command": "D:\\ESP_IDF\\idf_tools\\tools\\xtensa-esp-elf\\esp-13.2.0_20240530\\xtensa-esp-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.4\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/Desktop/i2c_oled/build/bootloader/config -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/log/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_common/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/dma/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/ldo/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/newlib/platform_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/deprecated_include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32s3\\interrupts.c.obj -c D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\soc\\esp32s3\\interrupts.c", "file": "D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\soc\\esp32s3\\interrupts.c", "output": "esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32s3\\interrupts.c.obj"}, {"directory": "C:/Users/<USER>/Desktop/i2c_oled/build/bootloader", "command": "D:\\ESP_IDF\\idf_tools\\tools\\xtensa-esp-elf\\esp-13.2.0_20240530\\xtensa-esp-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.4\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/Desktop/i2c_oled/build/bootloader/config -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/log/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_common/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/dma/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/ldo/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/newlib/platform_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/deprecated_include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32s3\\gpio_periph.c.obj -c D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\soc\\esp32s3\\gpio_periph.c", "file": "D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\soc\\esp32s3\\gpio_periph.c", "output": "esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32s3\\gpio_periph.c.obj"}, {"directory": "C:/Users/<USER>/Desktop/i2c_oled/build/bootloader", "command": "D:\\ESP_IDF\\idf_tools\\tools\\xtensa-esp-elf\\esp-13.2.0_20240530\\xtensa-esp-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.4\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/Desktop/i2c_oled/build/bootloader/config -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/log/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_common/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/dma/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/ldo/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/newlib/platform_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/deprecated_include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32s3\\uart_periph.c.obj -c D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\soc\\esp32s3\\uart_periph.c", "file": "D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\soc\\esp32s3\\uart_periph.c", "output": "esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32s3\\uart_periph.c.obj"}, {"directory": "C:/Users/<USER>/Desktop/i2c_oled/build/bootloader", "command": "D:\\ESP_IDF\\idf_tools\\tools\\xtensa-esp-elf\\esp-13.2.0_20240530\\xtensa-esp-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.4\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/Desktop/i2c_oled/build/bootloader/config -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/log/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_common/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/dma/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/ldo/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/newlib/platform_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/deprecated_include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32s3\\adc_periph.c.obj -c D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\soc\\esp32s3\\adc_periph.c", "file": "D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\soc\\esp32s3\\adc_periph.c", "output": "esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32s3\\adc_periph.c.obj"}, {"directory": "C:/Users/<USER>/Desktop/i2c_oled/build/bootloader", "command": "D:\\ESP_IDF\\idf_tools\\tools\\xtensa-esp-elf\\esp-13.2.0_20240530\\xtensa-esp-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.4\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/Desktop/i2c_oled/build/bootloader/config -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/log/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_common/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/dma/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/ldo/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/newlib/platform_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/deprecated_include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32s3\\dedic_gpio_periph.c.obj -c D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\soc\\esp32s3\\dedic_gpio_periph.c", "file": "D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\soc\\esp32s3\\dedic_gpio_periph.c", "output": "esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32s3\\dedic_gpio_periph.c.obj"}, {"directory": "C:/Users/<USER>/Desktop/i2c_oled/build/bootloader", "command": "D:\\ESP_IDF\\idf_tools\\tools\\xtensa-esp-elf\\esp-13.2.0_20240530\\xtensa-esp-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.4\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/Desktop/i2c_oled/build/bootloader/config -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/log/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_common/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/dma/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/ldo/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/newlib/platform_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/deprecated_include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32s3\\gdma_periph.c.obj -c D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\soc\\esp32s3\\gdma_periph.c", "file": "D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\soc\\esp32s3\\gdma_periph.c", "output": "esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32s3\\gdma_periph.c.obj"}, {"directory": "C:/Users/<USER>/Desktop/i2c_oled/build/bootloader", "command": "D:\\ESP_IDF\\idf_tools\\tools\\xtensa-esp-elf\\esp-13.2.0_20240530\\xtensa-esp-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.4\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/Desktop/i2c_oled/build/bootloader/config -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/log/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_common/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/dma/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/ldo/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/newlib/platform_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/deprecated_include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32s3\\spi_periph.c.obj -c D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\soc\\esp32s3\\spi_periph.c", "file": "D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\soc\\esp32s3\\spi_periph.c", "output": "esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32s3\\spi_periph.c.obj"}, {"directory": "C:/Users/<USER>/Desktop/i2c_oled/build/bootloader", "command": "D:\\ESP_IDF\\idf_tools\\tools\\xtensa-esp-elf\\esp-13.2.0_20240530\\xtensa-esp-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.4\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/Desktop/i2c_oled/build/bootloader/config -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/log/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_common/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/dma/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/ldo/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/newlib/platform_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/deprecated_include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32s3\\ledc_periph.c.obj -c D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\soc\\esp32s3\\ledc_periph.c", "file": "D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\soc\\esp32s3\\ledc_periph.c", "output": "esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32s3\\ledc_periph.c.obj"}, {"directory": "C:/Users/<USER>/Desktop/i2c_oled/build/bootloader", "command": "D:\\ESP_IDF\\idf_tools\\tools\\xtensa-esp-elf\\esp-13.2.0_20240530\\xtensa-esp-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.4\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/Desktop/i2c_oled/build/bootloader/config -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/log/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_common/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/dma/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/ldo/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/newlib/platform_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/deprecated_include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32s3\\pcnt_periph.c.obj -c D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\soc\\esp32s3\\pcnt_periph.c", "file": "D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\soc\\esp32s3\\pcnt_periph.c", "output": "esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32s3\\pcnt_periph.c.obj"}, {"directory": "C:/Users/<USER>/Desktop/i2c_oled/build/bootloader", "command": "D:\\ESP_IDF\\idf_tools\\tools\\xtensa-esp-elf\\esp-13.2.0_20240530\\xtensa-esp-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.4\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/Desktop/i2c_oled/build/bootloader/config -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/log/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_common/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/dma/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/ldo/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/newlib/platform_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/deprecated_include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32s3\\rmt_periph.c.obj -c D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\soc\\esp32s3\\rmt_periph.c", "file": "D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\soc\\esp32s3\\rmt_periph.c", "output": "esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32s3\\rmt_periph.c.obj"}, {"directory": "C:/Users/<USER>/Desktop/i2c_oled/build/bootloader", "command": "D:\\ESP_IDF\\idf_tools\\tools\\xtensa-esp-elf\\esp-13.2.0_20240530\\xtensa-esp-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.4\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/Desktop/i2c_oled/build/bootloader/config -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/log/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_common/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/dma/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/ldo/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/newlib/platform_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/deprecated_include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32s3\\sdm_periph.c.obj -c D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\soc\\esp32s3\\sdm_periph.c", "file": "D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\soc\\esp32s3\\sdm_periph.c", "output": "esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32s3\\sdm_periph.c.obj"}, {"directory": "C:/Users/<USER>/Desktop/i2c_oled/build/bootloader", "command": "D:\\ESP_IDF\\idf_tools\\tools\\xtensa-esp-elf\\esp-13.2.0_20240530\\xtensa-esp-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.4\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/Desktop/i2c_oled/build/bootloader/config -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/log/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_common/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/dma/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/ldo/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/newlib/platform_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/deprecated_include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32s3\\i2s_periph.c.obj -c D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\soc\\esp32s3\\i2s_periph.c", "file": "D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\soc\\esp32s3\\i2s_periph.c", "output": "esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32s3\\i2s_periph.c.obj"}, {"directory": "C:/Users/<USER>/Desktop/i2c_oled/build/bootloader", "command": "D:\\ESP_IDF\\idf_tools\\tools\\xtensa-esp-elf\\esp-13.2.0_20240530\\xtensa-esp-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.4\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/Desktop/i2c_oled/build/bootloader/config -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/log/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_common/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/dma/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/ldo/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/newlib/platform_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/deprecated_include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32s3\\i2c_periph.c.obj -c D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\soc\\esp32s3\\i2c_periph.c", "file": "D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\soc\\esp32s3\\i2c_periph.c", "output": "esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32s3\\i2c_periph.c.obj"}, {"directory": "C:/Users/<USER>/Desktop/i2c_oled/build/bootloader", "command": "D:\\ESP_IDF\\idf_tools\\tools\\xtensa-esp-elf\\esp-13.2.0_20240530\\xtensa-esp-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.4\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/Desktop/i2c_oled/build/bootloader/config -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/log/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_common/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/dma/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/ldo/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/newlib/platform_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/deprecated_include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32s3\\temperature_sensor_periph.c.obj -c D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\soc\\esp32s3\\temperature_sensor_periph.c", "file": "D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\soc\\esp32s3\\temperature_sensor_periph.c", "output": "esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32s3\\temperature_sensor_periph.c.obj"}, {"directory": "C:/Users/<USER>/Desktop/i2c_oled/build/bootloader", "command": "D:\\ESP_IDF\\idf_tools\\tools\\xtensa-esp-elf\\esp-13.2.0_20240530\\xtensa-esp-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.4\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/Desktop/i2c_oled/build/bootloader/config -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/log/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_common/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/dma/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/ldo/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/newlib/platform_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/deprecated_include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32s3\\timer_periph.c.obj -c D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\soc\\esp32s3\\timer_periph.c", "file": "D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\soc\\esp32s3\\timer_periph.c", "output": "esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32s3\\timer_periph.c.obj"}, {"directory": "C:/Users/<USER>/Desktop/i2c_oled/build/bootloader", "command": "D:\\ESP_IDF\\idf_tools\\tools\\xtensa-esp-elf\\esp-13.2.0_20240530\\xtensa-esp-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.4\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/Desktop/i2c_oled/build/bootloader/config -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/log/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_common/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/dma/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/ldo/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/newlib/platform_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/deprecated_include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32s3\\lcd_periph.c.obj -c D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\soc\\esp32s3\\lcd_periph.c", "file": "D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\soc\\esp32s3\\lcd_periph.c", "output": "esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32s3\\lcd_periph.c.obj"}, {"directory": "C:/Users/<USER>/Desktop/i2c_oled/build/bootloader", "command": "D:\\ESP_IDF\\idf_tools\\tools\\xtensa-esp-elf\\esp-13.2.0_20240530\\xtensa-esp-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.4\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/Desktop/i2c_oled/build/bootloader/config -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/log/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_common/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/dma/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/ldo/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/newlib/platform_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/deprecated_include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32s3\\mcpwm_periph.c.obj -c D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\soc\\esp32s3\\mcpwm_periph.c", "file": "D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\soc\\esp32s3\\mcpwm_periph.c", "output": "esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32s3\\mcpwm_periph.c.obj"}, {"directory": "C:/Users/<USER>/Desktop/i2c_oled/build/bootloader", "command": "D:\\ESP_IDF\\idf_tools\\tools\\xtensa-esp-elf\\esp-13.2.0_20240530\\xtensa-esp-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.4\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/Desktop/i2c_oled/build/bootloader/config -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/log/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_common/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/dma/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/ldo/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/newlib/platform_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/deprecated_include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32s3\\mpi_periph.c.obj -c D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\soc\\esp32s3\\mpi_periph.c", "file": "D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\soc\\esp32s3\\mpi_periph.c", "output": "esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32s3\\mpi_periph.c.obj"}, {"directory": "C:/Users/<USER>/Desktop/i2c_oled/build/bootloader", "command": "D:\\ESP_IDF\\idf_tools\\tools\\xtensa-esp-elf\\esp-13.2.0_20240530\\xtensa-esp-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.4\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/Desktop/i2c_oled/build/bootloader/config -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/log/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_common/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/dma/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/ldo/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/newlib/platform_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/deprecated_include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32s3\\sdmmc_periph.c.obj -c D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\soc\\esp32s3\\sdmmc_periph.c", "file": "D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\soc\\esp32s3\\sdmmc_periph.c", "output": "esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32s3\\sdmmc_periph.c.obj"}, {"directory": "C:/Users/<USER>/Desktop/i2c_oled/build/bootloader", "command": "D:\\ESP_IDF\\idf_tools\\tools\\xtensa-esp-elf\\esp-13.2.0_20240530\\xtensa-esp-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.4\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/Desktop/i2c_oled/build/bootloader/config -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/log/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_common/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/dma/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/ldo/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/newlib/platform_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/deprecated_include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32s3\\touch_sensor_periph.c.obj -c D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\soc\\esp32s3\\touch_sensor_periph.c", "file": "D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\soc\\esp32s3\\touch_sensor_periph.c", "output": "esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32s3\\touch_sensor_periph.c.obj"}, {"directory": "C:/Users/<USER>/Desktop/i2c_oled/build/bootloader", "command": "D:\\ESP_IDF\\idf_tools\\tools\\xtensa-esp-elf\\esp-13.2.0_20240530\\xtensa-esp-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.4\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/Desktop/i2c_oled/build/bootloader/config -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/log/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_common/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/dma/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/ldo/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/newlib/platform_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/deprecated_include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32s3\\twai_periph.c.obj -c D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\soc\\esp32s3\\twai_periph.c", "file": "D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\soc\\esp32s3\\twai_periph.c", "output": "esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32s3\\twai_periph.c.obj"}, {"directory": "C:/Users/<USER>/Desktop/i2c_oled/build/bootloader", "command": "D:\\ESP_IDF\\idf_tools\\tools\\xtensa-esp-elf\\esp-13.2.0_20240530\\xtensa-esp-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.4\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/Desktop/i2c_oled/build/bootloader/config -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/log/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_common/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/dma/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/ldo/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/newlib/platform_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/deprecated_include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32s3\\wdt_periph.c.obj -c D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\soc\\esp32s3\\wdt_periph.c", "file": "D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\soc\\esp32s3\\wdt_periph.c", "output": "esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32s3\\wdt_periph.c.obj"}, {"directory": "C:/Users/<USER>/Desktop/i2c_oled/build/bootloader", "command": "D:\\ESP_IDF\\idf_tools\\tools\\xtensa-esp-elf\\esp-13.2.0_20240530\\xtensa-esp-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.4\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/Desktop/i2c_oled/build/bootloader/config -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/log/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_common/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/dma/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/ldo/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/newlib/platform_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/deprecated_include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32s3\\usb_dwc_periph.c.obj -c D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\soc\\esp32s3\\usb_dwc_periph.c", "file": "D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\soc\\esp32s3\\usb_dwc_periph.c", "output": "esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32s3\\usb_dwc_periph.c.obj"}, {"directory": "C:/Users/<USER>/Desktop/i2c_oled/build/bootloader", "command": "D:\\ESP_IDF\\idf_tools\\tools\\xtensa-esp-elf\\esp-13.2.0_20240530\\xtensa-esp-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.4\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/Desktop/i2c_oled/build/bootloader/config -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/log/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_common/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/dma/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/ldo/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/newlib/platform_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/deprecated_include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32s3\\rtc_io_periph.c.obj -c D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\soc\\esp32s3\\rtc_io_periph.c", "file": "D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\soc\\esp32s3\\rtc_io_periph.c", "output": "esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32s3\\rtc_io_periph.c.obj"}, {"directory": "C:/Users/<USER>/Desktop/i2c_oled/build/bootloader", "command": "D:\\ESP_IDF\\idf_tools\\tools\\xtensa-esp-elf\\esp-13.2.0_20240530\\xtensa-esp-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.4\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/Desktop/i2c_oled/build/bootloader/config -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/subproject/components/micro-ecc -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/subproject/components/micro-ecc/micro-ecc -ID:/ESP_IDF/v5.3.4/esp-idf/components/log/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_common/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/dma/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/ldo/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/newlib/platform_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/deprecated_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\micro-ecc\\CMakeFiles\\__idf_micro-ecc.dir\\uECC_verify_antifault.c.obj -c D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\bootloader\\subproject\\components\\micro-ecc\\uECC_verify_antifault.c", "file": "D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\bootloader\\subproject\\components\\micro-ecc\\uECC_verify_antifault.c", "output": "esp-idf\\micro-ecc\\CMakeFiles\\__idf_micro-ecc.dir\\uECC_verify_antifault.c.obj"}, {"directory": "C:/Users/<USER>/Desktop/i2c_oled/build/bootloader", "command": "D:\\ESP_IDF\\idf_tools\\tools\\xtensa-esp-elf\\esp-13.2.0_20240530\\xtensa-esp-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.4\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/Desktop/i2c_oled/build/bootloader/config -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/platform_port/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/log/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_common/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/dma/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/ldo/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/newlib/platform_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/deprecated_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\hal\\CMakeFiles\\__idf_hal.dir\\hal_utils.c.obj -c D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\hal\\hal_utils.c", "file": "D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\hal\\hal_utils.c", "output": "esp-idf\\hal\\CMakeFiles\\__idf_hal.dir\\hal_utils.c.obj"}, {"directory": "C:/Users/<USER>/Desktop/i2c_oled/build/bootloader", "command": "D:\\ESP_IDF\\idf_tools\\tools\\xtensa-esp-elf\\esp-13.2.0_20240530\\xtensa-esp-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.4\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/Desktop/i2c_oled/build/bootloader/config -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/platform_port/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/log/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_common/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/dma/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/ldo/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/newlib/platform_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/deprecated_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\hal\\CMakeFiles\\__idf_hal.dir\\mpu_hal.c.obj -c D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\hal\\mpu_hal.c", "file": "D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\hal\\mpu_hal.c", "output": "esp-idf\\hal\\CMakeFiles\\__idf_hal.dir\\mpu_hal.c.obj"}, {"directory": "C:/Users/<USER>/Desktop/i2c_oled/build/bootloader", "command": "D:\\ESP_IDF\\idf_tools\\tools\\xtensa-esp-elf\\esp-13.2.0_20240530\\xtensa-esp-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.4\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/Desktop/i2c_oled/build/bootloader/config -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/platform_port/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/log/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_common/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/dma/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/ldo/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/newlib/platform_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/deprecated_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\hal\\CMakeFiles\\__idf_hal.dir\\efuse_hal.c.obj -c D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\hal\\efuse_hal.c", "file": "D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\hal\\efuse_hal.c", "output": "esp-idf\\hal\\CMakeFiles\\__idf_hal.dir\\efuse_hal.c.obj"}, {"directory": "C:/Users/<USER>/Desktop/i2c_oled/build/bootloader", "command": "D:\\ESP_IDF\\idf_tools\\tools\\xtensa-esp-elf\\esp-13.2.0_20240530\\xtensa-esp-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.4\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/Desktop/i2c_oled/build/bootloader/config -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/platform_port/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/log/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_common/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/dma/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/ldo/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/newlib/platform_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/deprecated_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\hal\\CMakeFiles\\__idf_hal.dir\\esp32s3\\efuse_hal.c.obj -c D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\hal\\esp32s3\\efuse_hal.c", "file": "D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\hal\\esp32s3\\efuse_hal.c", "output": "esp-idf\\hal\\CMakeFiles\\__idf_hal.dir\\esp32s3\\efuse_hal.c.obj"}, {"directory": "C:/Users/<USER>/Desktop/i2c_oled/build/bootloader", "command": "D:\\ESP_IDF\\idf_tools\\tools\\xtensa-esp-elf\\esp-13.2.0_20240530\\xtensa-esp-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.4\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/Desktop/i2c_oled/build/bootloader/config -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/platform_port/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/log/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_common/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/dma/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/ldo/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/newlib/platform_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/deprecated_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\hal\\CMakeFiles\\__idf_hal.dir\\mmu_hal.c.obj -c D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\hal\\mmu_hal.c", "file": "D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\hal\\mmu_hal.c", "output": "esp-idf\\hal\\CMakeFiles\\__idf_hal.dir\\mmu_hal.c.obj"}, {"directory": "C:/Users/<USER>/Desktop/i2c_oled/build/bootloader", "command": "D:\\ESP_IDF\\idf_tools\\tools\\xtensa-esp-elf\\esp-13.2.0_20240530\\xtensa-esp-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.4\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/Desktop/i2c_oled/build/bootloader/config -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/platform_port/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/log/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_common/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/dma/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/ldo/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/newlib/platform_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/deprecated_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\hal\\CMakeFiles\\__idf_hal.dir\\cache_hal.c.obj -c D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\hal\\cache_hal.c", "file": "D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\hal\\cache_hal.c", "output": "esp-idf\\hal\\CMakeFiles\\__idf_hal.dir\\cache_hal.c.obj"}, {"directory": "C:/Users/<USER>/Desktop/i2c_oled/build/bootloader", "command": "D:\\ESP_IDF\\idf_tools\\tools\\xtensa-esp-elf\\esp-13.2.0_20240530\\xtensa-esp-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.4\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/Desktop/i2c_oled/build/bootloader/config -ID:/ESP_IDF/v5.3.4/esp-idf/components/spi_flash/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/spi_flash/include/spi_flash -ID:/ESP_IDF/v5.3.4/esp-idf/components/log/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_common/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/dma/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/ldo/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/newlib/platform_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/deprecated_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/platform_port/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader_support/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader_support/bootloader_flash/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader_support/private_include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\spi_flash\\CMakeFiles\\__idf_spi_flash.dir\\spi_flash_wrap.c.obj -c D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\spi_flash\\spi_flash_wrap.c", "file": "D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\spi_flash\\spi_flash_wrap.c", "output": "esp-idf\\spi_flash\\CMakeFiles\\__idf_spi_flash.dir\\spi_flash_wrap.c.obj"}, {"directory": "C:/Users/<USER>/Desktop/i2c_oled/build/bootloader", "command": "D:\\ESP_IDF\\idf_tools\\tools\\xtensa-esp-elf\\esp-13.2.0_20240530\\xtensa-esp-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.4\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/Desktop/i2c_oled/build/bootloader/config -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_bootloader_format/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/log/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_common/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/dma/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/ldo/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/newlib/platform_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/deprecated_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\esp_bootloader_format\\CMakeFiles\\__idf_esp_bootloader_format.dir\\esp_bootloader_desc.c.obj -c D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\esp_bootloader_format\\esp_bootloader_desc.c", "file": "D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\esp_bootloader_format\\esp_bootloader_desc.c", "output": "esp-idf\\esp_bootloader_format\\CMakeFiles\\__idf_esp_bootloader_format.dir\\esp_bootloader_desc.c.obj"}, {"directory": "C:/Users/<USER>/Desktop/i2c_oled/build/bootloader", "command": "D:\\ESP_IDF\\idf_tools\\tools\\xtensa-esp-elf\\esp-13.2.0_20240530\\xtensa-esp-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.4\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/Desktop/i2c_oled/build/bootloader/config -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader_support/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader_support/bootloader_flash/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader_support/private_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/log/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_common/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/dma/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/ldo/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/newlib/platform_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/deprecated_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/subproject/components/micro-ecc -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/subproject/components/micro-ecc/micro-ecc -ID:/ESP_IDF/v5.3.4/esp-idf/components/spi_flash/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/platform_port/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/efuse/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/efuse/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_bootloader_format/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_app_format/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\bootloader_common.c.obj -c D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\bootloader_support\\src\\bootloader_common.c", "file": "D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\bootloader_support\\src\\bootloader_common.c", "output": "esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\bootloader_common.c.obj"}, {"directory": "C:/Users/<USER>/Desktop/i2c_oled/build/bootloader", "command": "D:\\ESP_IDF\\idf_tools\\tools\\xtensa-esp-elf\\esp-13.2.0_20240530\\xtensa-esp-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.4\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/Desktop/i2c_oled/build/bootloader/config -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader_support/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader_support/bootloader_flash/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader_support/private_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/log/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_common/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/dma/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/ldo/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/newlib/platform_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/deprecated_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/subproject/components/micro-ecc -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/subproject/components/micro-ecc/micro-ecc -ID:/ESP_IDF/v5.3.4/esp-idf/components/spi_flash/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/platform_port/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/efuse/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/efuse/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_bootloader_format/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_app_format/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\bootloader_common_loader.c.obj -c D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\bootloader_support\\src\\bootloader_common_loader.c", "file": "D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\bootloader_support\\src\\bootloader_common_loader.c", "output": "esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\bootloader_common_loader.c.obj"}, {"directory": "C:/Users/<USER>/Desktop/i2c_oled/build/bootloader", "command": "D:\\ESP_IDF\\idf_tools\\tools\\xtensa-esp-elf\\esp-13.2.0_20240530\\xtensa-esp-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.4\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/Desktop/i2c_oled/build/bootloader/config -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader_support/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader_support/bootloader_flash/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader_support/private_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/log/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_common/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/dma/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/ldo/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/newlib/platform_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/deprecated_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/subproject/components/micro-ecc -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/subproject/components/micro-ecc/micro-ecc -ID:/ESP_IDF/v5.3.4/esp-idf/components/spi_flash/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/platform_port/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/efuse/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/efuse/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_bootloader_format/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_app_format/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\bootloader_clock_init.c.obj -c D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\bootloader_support\\src\\bootloader_clock_init.c", "file": "D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\bootloader_support\\src\\bootloader_clock_init.c", "output": "esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\bootloader_clock_init.c.obj"}, {"directory": "C:/Users/<USER>/Desktop/i2c_oled/build/bootloader", "command": "D:\\ESP_IDF\\idf_tools\\tools\\xtensa-esp-elf\\esp-13.2.0_20240530\\xtensa-esp-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.4\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/Desktop/i2c_oled/build/bootloader/config -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader_support/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader_support/bootloader_flash/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader_support/private_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/log/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_common/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/dma/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/ldo/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/newlib/platform_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/deprecated_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/subproject/components/micro-ecc -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/subproject/components/micro-ecc/micro-ecc -ID:/ESP_IDF/v5.3.4/esp-idf/components/spi_flash/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/platform_port/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/efuse/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/efuse/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_bootloader_format/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_app_format/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\bootloader_mem.c.obj -c D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\bootloader_support\\src\\bootloader_mem.c", "file": "D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\bootloader_support\\src\\bootloader_mem.c", "output": "esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\bootloader_mem.c.obj"}, {"directory": "C:/Users/<USER>/Desktop/i2c_oled/build/bootloader", "command": "D:\\ESP_IDF\\idf_tools\\tools\\xtensa-esp-elf\\esp-13.2.0_20240530\\xtensa-esp-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.4\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/Desktop/i2c_oled/build/bootloader/config -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader_support/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader_support/bootloader_flash/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader_support/private_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/log/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_common/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/dma/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/ldo/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/newlib/platform_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/deprecated_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/subproject/components/micro-ecc -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/subproject/components/micro-ecc/micro-ecc -ID:/ESP_IDF/v5.3.4/esp-idf/components/spi_flash/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/platform_port/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/efuse/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/efuse/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_bootloader_format/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_app_format/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\bootloader_random.c.obj -c D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\bootloader_support\\src\\bootloader_random.c", "file": "D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\bootloader_support\\src\\bootloader_random.c", "output": "esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\bootloader_random.c.obj"}, {"directory": "C:/Users/<USER>/Desktop/i2c_oled/build/bootloader", "command": "D:\\ESP_IDF\\idf_tools\\tools\\xtensa-esp-elf\\esp-13.2.0_20240530\\xtensa-esp-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.4\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/Desktop/i2c_oled/build/bootloader/config -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader_support/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader_support/bootloader_flash/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader_support/private_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/log/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_common/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/dma/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/ldo/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/newlib/platform_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/deprecated_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/subproject/components/micro-ecc -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/subproject/components/micro-ecc/micro-ecc -ID:/ESP_IDF/v5.3.4/esp-idf/components/spi_flash/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/platform_port/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/efuse/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/efuse/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_bootloader_format/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_app_format/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\bootloader_efuse.c.obj -c D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\bootloader_support\\src\\bootloader_efuse.c", "file": "D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\bootloader_support\\src\\bootloader_efuse.c", "output": "esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\bootloader_efuse.c.obj"}, {"directory": "C:/Users/<USER>/Desktop/i2c_oled/build/bootloader", "command": "D:\\ESP_IDF\\idf_tools\\tools\\xtensa-esp-elf\\esp-13.2.0_20240530\\xtensa-esp-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.4\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/Desktop/i2c_oled/build/bootloader/config -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader_support/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader_support/bootloader_flash/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader_support/private_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/log/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_common/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/dma/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/ldo/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/newlib/platform_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/deprecated_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/subproject/components/micro-ecc -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/subproject/components/micro-ecc/micro-ecc -ID:/ESP_IDF/v5.3.4/esp-idf/components/spi_flash/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/platform_port/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/efuse/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/efuse/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_bootloader_format/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_app_format/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\flash_encrypt.c.obj -c D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\bootloader_support\\src\\flash_encrypt.c", "file": "D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\bootloader_support\\src\\flash_encrypt.c", "output": "esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\flash_encrypt.c.obj"}, {"directory": "C:/Users/<USER>/Desktop/i2c_oled/build/bootloader", "command": "D:\\ESP_IDF\\idf_tools\\tools\\xtensa-esp-elf\\esp-13.2.0_20240530\\xtensa-esp-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.4\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/Desktop/i2c_oled/build/bootloader/config -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader_support/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader_support/bootloader_flash/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader_support/private_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/log/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_common/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/dma/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/ldo/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/newlib/platform_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/deprecated_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/subproject/components/micro-ecc -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/subproject/components/micro-ecc/micro-ecc -ID:/ESP_IDF/v5.3.4/esp-idf/components/spi_flash/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/platform_port/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/efuse/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/efuse/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_bootloader_format/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_app_format/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\secure_boot.c.obj -c D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\bootloader_support\\src\\secure_boot.c", "file": "D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\bootloader_support\\src\\secure_boot.c", "output": "esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\secure_boot.c.obj"}, {"directory": "C:/Users/<USER>/Desktop/i2c_oled/build/bootloader", "command": "D:\\ESP_IDF\\idf_tools\\tools\\xtensa-esp-elf\\esp-13.2.0_20240530\\xtensa-esp-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.4\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/Desktop/i2c_oled/build/bootloader/config -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader_support/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader_support/bootloader_flash/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader_support/private_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/log/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_common/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/dma/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/ldo/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/newlib/platform_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/deprecated_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/subproject/components/micro-ecc -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/subproject/components/micro-ecc/micro-ecc -ID:/ESP_IDF/v5.3.4/esp-idf/components/spi_flash/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/platform_port/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/efuse/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/efuse/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_bootloader_format/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_app_format/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\bootloader_random_esp32s3.c.obj -c D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\bootloader_support\\src\\bootloader_random_esp32s3.c", "file": "D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\bootloader_support\\src\\bootloader_random_esp32s3.c", "output": "esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\bootloader_random_esp32s3.c.obj"}, {"directory": "C:/Users/<USER>/Desktop/i2c_oled/build/bootloader", "command": "D:\\ESP_IDF\\idf_tools\\tools\\xtensa-esp-elf\\esp-13.2.0_20240530\\xtensa-esp-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.4\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/Desktop/i2c_oled/build/bootloader/config -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader_support/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader_support/bootloader_flash/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader_support/private_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/log/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_common/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/dma/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/ldo/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/newlib/platform_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/deprecated_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/subproject/components/micro-ecc -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/subproject/components/micro-ecc/micro-ecc -ID:/ESP_IDF/v5.3.4/esp-idf/components/spi_flash/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/platform_port/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/efuse/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/efuse/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_bootloader_format/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_app_format/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\bootloader_flash\\src\\bootloader_flash.c.obj -c D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\bootloader_support\\bootloader_flash\\src\\bootloader_flash.c", "file": "D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\bootloader_support\\bootloader_flash\\src\\bootloader_flash.c", "output": "esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\bootloader_flash\\src\\bootloader_flash.c.obj"}, {"directory": "C:/Users/<USER>/Desktop/i2c_oled/build/bootloader", "command": "D:\\ESP_IDF\\idf_tools\\tools\\xtensa-esp-elf\\esp-13.2.0_20240530\\xtensa-esp-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.4\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/Desktop/i2c_oled/build/bootloader/config -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader_support/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader_support/bootloader_flash/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader_support/private_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/log/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_common/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/dma/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/ldo/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/newlib/platform_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/deprecated_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/subproject/components/micro-ecc -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/subproject/components/micro-ecc/micro-ecc -ID:/ESP_IDF/v5.3.4/esp-idf/components/spi_flash/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/platform_port/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/efuse/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/efuse/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_bootloader_format/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_app_format/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\bootloader_flash\\src\\flash_qio_mode.c.obj -c D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\bootloader_support\\bootloader_flash\\src\\flash_qio_mode.c", "file": "D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\bootloader_support\\bootloader_flash\\src\\flash_qio_mode.c", "output": "esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\bootloader_flash\\src\\flash_qio_mode.c.obj"}, {"directory": "C:/Users/<USER>/Desktop/i2c_oled/build/bootloader", "command": "D:\\ESP_IDF\\idf_tools\\tools\\xtensa-esp-elf\\esp-13.2.0_20240530\\xtensa-esp-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.4\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/Desktop/i2c_oled/build/bootloader/config -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader_support/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader_support/bootloader_flash/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader_support/private_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/log/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_common/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/dma/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/ldo/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/newlib/platform_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/deprecated_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/subproject/components/micro-ecc -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/subproject/components/micro-ecc/micro-ecc -ID:/ESP_IDF/v5.3.4/esp-idf/components/spi_flash/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/platform_port/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/efuse/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/efuse/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_bootloader_format/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_app_format/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\bootloader_flash\\src\\bootloader_flash_config_esp32s3.c.obj -c D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\bootloader_support\\bootloader_flash\\src\\bootloader_flash_config_esp32s3.c", "file": "D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\bootloader_support\\bootloader_flash\\src\\bootloader_flash_config_esp32s3.c", "output": "esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\bootloader_flash\\src\\bootloader_flash_config_esp32s3.c.obj"}, {"directory": "C:/Users/<USER>/Desktop/i2c_oled/build/bootloader", "command": "D:\\ESP_IDF\\idf_tools\\tools\\xtensa-esp-elf\\esp-13.2.0_20240530\\xtensa-esp-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.4\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/Desktop/i2c_oled/build/bootloader/config -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader_support/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader_support/bootloader_flash/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader_support/private_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/log/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_common/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/dma/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/ldo/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/newlib/platform_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/deprecated_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/subproject/components/micro-ecc -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/subproject/components/micro-ecc/micro-ecc -ID:/ESP_IDF/v5.3.4/esp-idf/components/spi_flash/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/platform_port/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/efuse/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/efuse/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_bootloader_format/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_app_format/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\bootloader_utility.c.obj -c D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\bootloader_support\\src\\bootloader_utility.c", "file": "D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\bootloader_support\\src\\bootloader_utility.c", "output": "esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\bootloader_utility.c.obj"}, {"directory": "C:/Users/<USER>/Desktop/i2c_oled/build/bootloader", "command": "D:\\ESP_IDF\\idf_tools\\tools\\xtensa-esp-elf\\esp-13.2.0_20240530\\xtensa-esp-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.4\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/Desktop/i2c_oled/build/bootloader/config -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader_support/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader_support/bootloader_flash/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader_support/private_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/log/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_common/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/dma/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/ldo/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/newlib/platform_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/deprecated_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/subproject/components/micro-ecc -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/subproject/components/micro-ecc/micro-ecc -ID:/ESP_IDF/v5.3.4/esp-idf/components/spi_flash/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/platform_port/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/efuse/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/efuse/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_bootloader_format/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_app_format/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\flash_partitions.c.obj -c D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\bootloader_support\\src\\flash_partitions.c", "file": "D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\bootloader_support\\src\\flash_partitions.c", "output": "esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\flash_partitions.c.obj"}, {"directory": "C:/Users/<USER>/Desktop/i2c_oled/build/bootloader", "command": "D:\\ESP_IDF\\idf_tools\\tools\\xtensa-esp-elf\\esp-13.2.0_20240530\\xtensa-esp-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.4\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/Desktop/i2c_oled/build/bootloader/config -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader_support/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader_support/bootloader_flash/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader_support/private_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/log/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_common/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/dma/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/ldo/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/newlib/platform_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/deprecated_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/subproject/components/micro-ecc -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/subproject/components/micro-ecc/micro-ecc -ID:/ESP_IDF/v5.3.4/esp-idf/components/spi_flash/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/platform_port/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/efuse/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/efuse/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_bootloader_format/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_app_format/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\esp_image_format.c.obj -c D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\bootloader_support\\src\\esp_image_format.c", "file": "D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\bootloader_support\\src\\esp_image_format.c", "output": "esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\esp_image_format.c.obj"}, {"directory": "C:/Users/<USER>/Desktop/i2c_oled/build/bootloader", "command": "D:\\ESP_IDF\\idf_tools\\tools\\xtensa-esp-elf\\esp-13.2.0_20240530\\xtensa-esp-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.4\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/Desktop/i2c_oled/build/bootloader/config -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader_support/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader_support/bootloader_flash/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader_support/private_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/log/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_common/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/dma/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/ldo/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/newlib/platform_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/deprecated_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/subproject/components/micro-ecc -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/subproject/components/micro-ecc/micro-ecc -ID:/ESP_IDF/v5.3.4/esp-idf/components/spi_flash/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/platform_port/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/efuse/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/efuse/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_bootloader_format/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_app_format/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\bootloader_init.c.obj -c D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\bootloader_support\\src\\bootloader_init.c", "file": "D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\bootloader_support\\src\\bootloader_init.c", "output": "esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\bootloader_init.c.obj"}, {"directory": "C:/Users/<USER>/Desktop/i2c_oled/build/bootloader", "command": "D:\\ESP_IDF\\idf_tools\\tools\\xtensa-esp-elf\\esp-13.2.0_20240530\\xtensa-esp-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.4\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/Desktop/i2c_oled/build/bootloader/config -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader_support/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader_support/bootloader_flash/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader_support/private_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/log/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_common/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/dma/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/ldo/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/newlib/platform_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/deprecated_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/subproject/components/micro-ecc -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/subproject/components/micro-ecc/micro-ecc -ID:/ESP_IDF/v5.3.4/esp-idf/components/spi_flash/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/platform_port/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/efuse/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/efuse/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_bootloader_format/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_app_format/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\bootloader_clock_loader.c.obj -c D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\bootloader_support\\src\\bootloader_clock_loader.c", "file": "D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\bootloader_support\\src\\bootloader_clock_loader.c", "output": "esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\bootloader_clock_loader.c.obj"}, {"directory": "C:/Users/<USER>/Desktop/i2c_oled/build/bootloader", "command": "D:\\ESP_IDF\\idf_tools\\tools\\xtensa-esp-elf\\esp-13.2.0_20240530\\xtensa-esp-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.4\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/Desktop/i2c_oled/build/bootloader/config -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader_support/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader_support/bootloader_flash/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader_support/private_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/log/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_common/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/dma/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/ldo/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/newlib/platform_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/deprecated_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/subproject/components/micro-ecc -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/subproject/components/micro-ecc/micro-ecc -ID:/ESP_IDF/v5.3.4/esp-idf/components/spi_flash/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/platform_port/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/efuse/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/efuse/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_bootloader_format/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_app_format/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\bootloader_console.c.obj -c D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\bootloader_support\\src\\bootloader_console.c", "file": "D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\bootloader_support\\src\\bootloader_console.c", "output": "esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\bootloader_console.c.obj"}, {"directory": "C:/Users/<USER>/Desktop/i2c_oled/build/bootloader", "command": "D:\\ESP_IDF\\idf_tools\\tools\\xtensa-esp-elf\\esp-13.2.0_20240530\\xtensa-esp-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.4\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/Desktop/i2c_oled/build/bootloader/config -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader_support/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader_support/bootloader_flash/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader_support/private_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/log/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_common/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/dma/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/ldo/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/newlib/platform_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/deprecated_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/subproject/components/micro-ecc -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/subproject/components/micro-ecc/micro-ecc -ID:/ESP_IDF/v5.3.4/esp-idf/components/spi_flash/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/platform_port/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/efuse/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/efuse/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_bootloader_format/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_app_format/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\bootloader_console_loader.c.obj -c D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\bootloader_support\\src\\bootloader_console_loader.c", "file": "D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\bootloader_support\\src\\bootloader_console_loader.c", "output": "esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\bootloader_console_loader.c.obj"}, {"directory": "C:/Users/<USER>/Desktop/i2c_oled/build/bootloader", "command": "D:\\ESP_IDF\\idf_tools\\tools\\xtensa-esp-elf\\esp-13.2.0_20240530\\xtensa-esp-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.4\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/Desktop/i2c_oled/build/bootloader/config -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader_support/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader_support/bootloader_flash/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader_support/private_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/log/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_common/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/dma/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/ldo/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/newlib/platform_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/deprecated_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/subproject/components/micro-ecc -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/subproject/components/micro-ecc/micro-ecc -ID:/ESP_IDF/v5.3.4/esp-idf/components/spi_flash/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/platform_port/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/efuse/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/efuse/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_bootloader_format/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_app_format/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\esp32s3\\bootloader_sha.c.obj -c D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\bootloader_support\\src\\esp32s3\\bootloader_sha.c", "file": "D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\bootloader_support\\src\\esp32s3\\bootloader_sha.c", "output": "esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\esp32s3\\bootloader_sha.c.obj"}, {"directory": "C:/Users/<USER>/Desktop/i2c_oled/build/bootloader", "command": "D:\\ESP_IDF\\idf_tools\\tools\\xtensa-esp-elf\\esp-13.2.0_20240530\\xtensa-esp-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.4\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/Desktop/i2c_oled/build/bootloader/config -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader_support/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader_support/bootloader_flash/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader_support/private_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/log/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_common/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/dma/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/ldo/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/newlib/platform_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/deprecated_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/subproject/components/micro-ecc -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/subproject/components/micro-ecc/micro-ecc -ID:/ESP_IDF/v5.3.4/esp-idf/components/spi_flash/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/platform_port/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/efuse/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/efuse/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_bootloader_format/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_app_format/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\esp32s3\\bootloader_soc.c.obj -c D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\bootloader_support\\src\\esp32s3\\bootloader_soc.c", "file": "D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\bootloader_support\\src\\esp32s3\\bootloader_soc.c", "output": "esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\esp32s3\\bootloader_soc.c.obj"}, {"directory": "C:/Users/<USER>/Desktop/i2c_oled/build/bootloader", "command": "D:\\ESP_IDF\\idf_tools\\tools\\xtensa-esp-elf\\esp-13.2.0_20240530\\xtensa-esp-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.4\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/Desktop/i2c_oled/build/bootloader/config -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader_support/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader_support/bootloader_flash/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader_support/private_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/log/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_common/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/dma/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/ldo/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/newlib/platform_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/deprecated_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/subproject/components/micro-ecc -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/subproject/components/micro-ecc/micro-ecc -ID:/ESP_IDF/v5.3.4/esp-idf/components/spi_flash/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/platform_port/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/efuse/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/efuse/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_bootloader_format/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_app_format/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\esp32s3\\bootloader_esp32s3.c.obj -c D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\bootloader_support\\src\\esp32s3\\bootloader_esp32s3.c", "file": "D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\bootloader_support\\src\\esp32s3\\bootloader_esp32s3.c", "output": "esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\esp32s3\\bootloader_esp32s3.c.obj"}, {"directory": "C:/Users/<USER>/Desktop/i2c_oled/build/bootloader", "command": "D:\\ESP_IDF\\idf_tools\\tools\\xtensa-esp-elf\\esp-13.2.0_20240530\\xtensa-esp-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.4\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/Desktop/i2c_oled/build/bootloader/config -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader_support/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader_support/bootloader_flash/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader_support/private_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/log/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_common/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/dma/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/ldo/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/newlib/platform_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/deprecated_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/subproject/components/micro-ecc -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/subproject/components/micro-ecc/micro-ecc -ID:/ESP_IDF/v5.3.4/esp-idf/components/spi_flash/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/platform_port/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/efuse/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/efuse/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_bootloader_format/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_app_format/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\bootloader_panic.c.obj -c D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\bootloader_support\\src\\bootloader_panic.c", "file": "D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\bootloader_support\\src\\bootloader_panic.c", "output": "esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\bootloader_panic.c.obj"}, {"directory": "C:/Users/<USER>/Desktop/i2c_oled/build/bootloader", "command": "D:\\ESP_IDF\\idf_tools\\tools\\xtensa-esp-elf\\esp-13.2.0_20240530\\xtensa-esp-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.4\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/Desktop/i2c_oled/build/bootloader/config -ID:/ESP_IDF/v5.3.4/esp-idf/components/efuse/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/efuse/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/efuse/private_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/efuse/esp32s3/private_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/log/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_common/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/dma/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/ldo/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/newlib/platform_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/deprecated_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader_support/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader_support/bootloader_flash/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader_support/private_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/spi_flash/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/platform_port/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\efuse\\CMakeFiles\\__idf_efuse.dir\\esp32s3\\esp_efuse_table.c.obj -c D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\efuse\\esp32s3\\esp_efuse_table.c", "file": "D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\efuse\\esp32s3\\esp_efuse_table.c", "output": "esp-idf\\efuse\\CMakeFiles\\__idf_efuse.dir\\esp32s3\\esp_efuse_table.c.obj"}, {"directory": "C:/Users/<USER>/Desktop/i2c_oled/build/bootloader", "command": "D:\\ESP_IDF\\idf_tools\\tools\\xtensa-esp-elf\\esp-13.2.0_20240530\\xtensa-esp-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.4\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/Desktop/i2c_oled/build/bootloader/config -ID:/ESP_IDF/v5.3.4/esp-idf/components/efuse/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/efuse/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/efuse/private_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/efuse/esp32s3/private_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/log/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_common/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/dma/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/ldo/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/newlib/platform_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/deprecated_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader_support/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader_support/bootloader_flash/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader_support/private_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/spi_flash/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/platform_port/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\efuse\\CMakeFiles\\__idf_efuse.dir\\esp32s3\\esp_efuse_fields.c.obj -c D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\efuse\\esp32s3\\esp_efuse_fields.c", "file": "D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\efuse\\esp32s3\\esp_efuse_fields.c", "output": "esp-idf\\efuse\\CMakeFiles\\__idf_efuse.dir\\esp32s3\\esp_efuse_fields.c.obj"}, {"directory": "C:/Users/<USER>/Desktop/i2c_oled/build/bootloader", "command": "D:\\ESP_IDF\\idf_tools\\tools\\xtensa-esp-elf\\esp-13.2.0_20240530\\xtensa-esp-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.4\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/Desktop/i2c_oled/build/bootloader/config -ID:/ESP_IDF/v5.3.4/esp-idf/components/efuse/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/efuse/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/efuse/private_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/efuse/esp32s3/private_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/log/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_common/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/dma/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/ldo/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/newlib/platform_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/deprecated_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader_support/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader_support/bootloader_flash/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader_support/private_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/spi_flash/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/platform_port/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\efuse\\CMakeFiles\\__idf_efuse.dir\\esp32s3\\esp_efuse_rtc_calib.c.obj -c D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\efuse\\esp32s3\\esp_efuse_rtc_calib.c", "file": "D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\efuse\\esp32s3\\esp_efuse_rtc_calib.c", "output": "esp-idf\\efuse\\CMakeFiles\\__idf_efuse.dir\\esp32s3\\esp_efuse_rtc_calib.c.obj"}, {"directory": "C:/Users/<USER>/Desktop/i2c_oled/build/bootloader", "command": "D:\\ESP_IDF\\idf_tools\\tools\\xtensa-esp-elf\\esp-13.2.0_20240530\\xtensa-esp-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.4\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/Desktop/i2c_oled/build/bootloader/config -ID:/ESP_IDF/v5.3.4/esp-idf/components/efuse/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/efuse/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/efuse/private_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/efuse/esp32s3/private_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/log/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_common/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/dma/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/ldo/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/newlib/platform_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/deprecated_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader_support/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader_support/bootloader_flash/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader_support/private_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/spi_flash/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/platform_port/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\efuse\\CMakeFiles\\__idf_efuse.dir\\esp32s3\\esp_efuse_utility.c.obj -c D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\efuse\\esp32s3\\esp_efuse_utility.c", "file": "D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\efuse\\esp32s3\\esp_efuse_utility.c", "output": "esp-idf\\efuse\\CMakeFiles\\__idf_efuse.dir\\esp32s3\\esp_efuse_utility.c.obj"}, {"directory": "C:/Users/<USER>/Desktop/i2c_oled/build/bootloader", "command": "D:\\ESP_IDF\\idf_tools\\tools\\xtensa-esp-elf\\esp-13.2.0_20240530\\xtensa-esp-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.4\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/Desktop/i2c_oled/build/bootloader/config -ID:/ESP_IDF/v5.3.4/esp-idf/components/efuse/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/efuse/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/efuse/private_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/efuse/esp32s3/private_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/log/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_common/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/dma/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/ldo/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/newlib/platform_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/deprecated_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader_support/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader_support/bootloader_flash/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader_support/private_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/spi_flash/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/platform_port/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\efuse\\CMakeFiles\\__idf_efuse.dir\\src\\esp_efuse_api.c.obj -c D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\efuse\\src\\esp_efuse_api.c", "file": "D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\efuse\\src\\esp_efuse_api.c", "output": "esp-idf\\efuse\\CMakeFiles\\__idf_efuse.dir\\src\\esp_efuse_api.c.obj"}, {"directory": "C:/Users/<USER>/Desktop/i2c_oled/build/bootloader", "command": "D:\\ESP_IDF\\idf_tools\\tools\\xtensa-esp-elf\\esp-13.2.0_20240530\\xtensa-esp-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.4\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/Desktop/i2c_oled/build/bootloader/config -ID:/ESP_IDF/v5.3.4/esp-idf/components/efuse/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/efuse/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/efuse/private_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/efuse/esp32s3/private_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/log/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_common/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/dma/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/ldo/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/newlib/platform_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/deprecated_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader_support/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader_support/bootloader_flash/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader_support/private_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/spi_flash/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/platform_port/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\efuse\\CMakeFiles\\__idf_efuse.dir\\src\\esp_efuse_fields.c.obj -c D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\efuse\\src\\esp_efuse_fields.c", "file": "D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\efuse\\src\\esp_efuse_fields.c", "output": "esp-idf\\efuse\\CMakeFiles\\__idf_efuse.dir\\src\\esp_efuse_fields.c.obj"}, {"directory": "C:/Users/<USER>/Desktop/i2c_oled/build/bootloader", "command": "D:\\ESP_IDF\\idf_tools\\tools\\xtensa-esp-elf\\esp-13.2.0_20240530\\xtensa-esp-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.4\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/Desktop/i2c_oled/build/bootloader/config -ID:/ESP_IDF/v5.3.4/esp-idf/components/efuse/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/efuse/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/efuse/private_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/efuse/esp32s3/private_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/log/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_common/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/dma/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/ldo/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/newlib/platform_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/deprecated_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader_support/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader_support/bootloader_flash/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader_support/private_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/spi_flash/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/platform_port/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\efuse\\CMakeFiles\\__idf_efuse.dir\\src\\esp_efuse_utility.c.obj -c D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\efuse\\src\\esp_efuse_utility.c", "file": "D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\efuse\\src\\esp_efuse_utility.c", "output": "esp-idf\\efuse\\CMakeFiles\\__idf_efuse.dir\\src\\esp_efuse_utility.c.obj"}, {"directory": "C:/Users/<USER>/Desktop/i2c_oled/build/bootloader", "command": "D:\\ESP_IDF\\idf_tools\\tools\\xtensa-esp-elf\\esp-13.2.0_20240530\\xtensa-esp-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.4\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/Desktop/i2c_oled/build/bootloader/config -ID:/ESP_IDF/v5.3.4/esp-idf/components/efuse/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/efuse/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/efuse/private_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/efuse/esp32s3/private_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/log/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_common/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/dma/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/ldo/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/newlib/platform_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/deprecated_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader_support/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader_support/bootloader_flash/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader_support/private_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/spi_flash/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/platform_port/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\efuse\\CMakeFiles\\__idf_efuse.dir\\src\\efuse_controller\\keys\\with_key_purposes\\esp_efuse_api_key.c.obj -c D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\efuse\\src\\efuse_controller\\keys\\with_key_purposes\\esp_efuse_api_key.c", "file": "D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\efuse\\src\\efuse_controller\\keys\\with_key_purposes\\esp_efuse_api_key.c", "output": "esp-idf\\efuse\\CMakeFiles\\__idf_efuse.dir\\src\\efuse_controller\\keys\\with_key_purposes\\esp_efuse_api_key.c.obj"}, {"directory": "C:/Users/<USER>/Desktop/i2c_oled/build/bootloader", "command": "D:\\ESP_IDF\\idf_tools\\tools\\xtensa-esp-elf\\esp-13.2.0_20240530\\xtensa-esp-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.4\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/Desktop/i2c_oled/build/bootloader/config -ID:/ESP_IDF/v5.3.4/esp-idf/components/log/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_common/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/dma/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/ldo/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/newlib/platform_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/deprecated_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/spi_flash/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/platform_port/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_app_format/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\esp_system\\CMakeFiles\\__idf_esp_system.dir\\esp_err.c.obj -c D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\esp_system\\esp_err.c", "file": "D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\esp_system\\esp_err.c", "output": "esp-idf\\esp_system\\CMakeFiles\\__idf_esp_system.dir\\esp_err.c.obj"}, {"directory": "C:/Users/<USER>/Desktop/i2c_oled/build/bootloader", "command": "D:\\ESP_IDF\\idf_tools\\tools\\xtensa-esp-elf\\esp-13.2.0_20240530\\xtensa-esp-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.4\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/Desktop/i2c_oled/build/bootloader/config -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/dma/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/ldo/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/esp_private -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/log/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_common/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/newlib/platform_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/deprecated_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/efuse/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/efuse/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/spi_flash/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/platform_port/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader_support/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader_support/bootloader_flash/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader_support/private_include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\esp_hw_support\\CMakeFiles\\__idf_esp_hw_support.dir\\cpu.c.obj -c D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\esp_hw_support\\cpu.c", "file": "D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\esp_hw_support\\cpu.c", "output": "esp-idf\\esp_hw_support\\CMakeFiles\\__idf_esp_hw_support.dir\\cpu.c.obj"}, {"directory": "C:/Users/<USER>/Desktop/i2c_oled/build/bootloader", "command": "D:\\ESP_IDF\\idf_tools\\tools\\xtensa-esp-elf\\esp-13.2.0_20240530\\xtensa-esp-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.4\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/Desktop/i2c_oled/build/bootloader/config -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/dma/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/ldo/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/esp_private -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/log/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_common/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/newlib/platform_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/deprecated_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/efuse/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/efuse/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/spi_flash/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/platform_port/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader_support/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader_support/bootloader_flash/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader_support/private_include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\esp_hw_support\\CMakeFiles\\__idf_esp_hw_support.dir\\port\\esp32s3\\esp_cpu_intr.c.obj -c D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\esp_hw_support\\port\\esp32s3\\esp_cpu_intr.c", "file": "D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\esp_hw_support\\port\\esp32s3\\esp_cpu_intr.c", "output": "esp-idf\\esp_hw_support\\CMakeFiles\\__idf_esp_hw_support.dir\\port\\esp32s3\\esp_cpu_intr.c.obj"}, {"directory": "C:/Users/<USER>/Desktop/i2c_oled/build/bootloader", "command": "D:\\ESP_IDF\\idf_tools\\tools\\xtensa-esp-elf\\esp-13.2.0_20240530\\xtensa-esp-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.4\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/Desktop/i2c_oled/build/bootloader/config -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/dma/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/ldo/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/esp_private -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/log/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_common/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/newlib/platform_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/deprecated_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/efuse/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/efuse/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/spi_flash/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/platform_port/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader_support/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader_support/bootloader_flash/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader_support/private_include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\esp_hw_support\\CMakeFiles\\__idf_esp_hw_support.dir\\esp_memory_utils.c.obj -c D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\esp_hw_support\\esp_memory_utils.c", "file": "D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\esp_hw_support\\esp_memory_utils.c", "output": "esp-idf\\esp_hw_support\\CMakeFiles\\__idf_esp_hw_support.dir\\esp_memory_utils.c.obj"}, {"directory": "C:/Users/<USER>/Desktop/i2c_oled/build/bootloader", "command": "D:\\ESP_IDF\\idf_tools\\tools\\xtensa-esp-elf\\esp-13.2.0_20240530\\xtensa-esp-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.4\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/Desktop/i2c_oled/build/bootloader/config -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/dma/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/ldo/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/esp_private -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/log/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_common/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/newlib/platform_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/deprecated_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/efuse/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/efuse/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/spi_flash/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/platform_port/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader_support/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader_support/bootloader_flash/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader_support/private_include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\esp_hw_support\\CMakeFiles\\__idf_esp_hw_support.dir\\port\\esp32s3\\cpu_region_protect.c.obj -c D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\esp_hw_support\\port\\esp32s3\\cpu_region_protect.c", "file": "D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\esp_hw_support\\port\\esp32s3\\cpu_region_protect.c", "output": "esp-idf\\esp_hw_support\\CMakeFiles\\__idf_esp_hw_support.dir\\port\\esp32s3\\cpu_region_protect.c.obj"}, {"directory": "C:/Users/<USER>/Desktop/i2c_oled/build/bootloader", "command": "D:\\ESP_IDF\\idf_tools\\tools\\xtensa-esp-elf\\esp-13.2.0_20240530\\xtensa-esp-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.4\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/Desktop/i2c_oled/build/bootloader/config -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/dma/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/ldo/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/esp_private -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/log/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_common/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/newlib/platform_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/deprecated_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/efuse/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/efuse/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/spi_flash/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/platform_port/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader_support/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader_support/bootloader_flash/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader_support/private_include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\esp_hw_support\\CMakeFiles\\__idf_esp_hw_support.dir\\port\\esp32s3\\rtc_clk.c.obj -c D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\esp_hw_support\\port\\esp32s3\\rtc_clk.c", "file": "D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\esp_hw_support\\port\\esp32s3\\rtc_clk.c", "output": "esp-idf\\esp_hw_support\\CMakeFiles\\__idf_esp_hw_support.dir\\port\\esp32s3\\rtc_clk.c.obj"}, {"directory": "C:/Users/<USER>/Desktop/i2c_oled/build/bootloader", "command": "D:\\ESP_IDF\\idf_tools\\tools\\xtensa-esp-elf\\esp-13.2.0_20240530\\xtensa-esp-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.4\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/Desktop/i2c_oled/build/bootloader/config -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/dma/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/ldo/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/esp_private -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/log/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_common/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/newlib/platform_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/deprecated_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/efuse/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/efuse/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/spi_flash/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/platform_port/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader_support/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader_support/bootloader_flash/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader_support/private_include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\esp_hw_support\\CMakeFiles\\__idf_esp_hw_support.dir\\port\\esp32s3\\rtc_clk_init.c.obj -c D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\esp_hw_support\\port\\esp32s3\\rtc_clk_init.c", "file": "D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\esp_hw_support\\port\\esp32s3\\rtc_clk_init.c", "output": "esp-idf\\esp_hw_support\\CMakeFiles\\__idf_esp_hw_support.dir\\port\\esp32s3\\rtc_clk_init.c.obj"}, {"directory": "C:/Users/<USER>/Desktop/i2c_oled/build/bootloader", "command": "D:\\ESP_IDF\\idf_tools\\tools\\xtensa-esp-elf\\esp-13.2.0_20240530\\xtensa-esp-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.4\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/Desktop/i2c_oled/build/bootloader/config -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/dma/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/ldo/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/esp_private -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/log/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_common/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/newlib/platform_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/deprecated_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/efuse/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/efuse/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/spi_flash/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/platform_port/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader_support/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader_support/bootloader_flash/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader_support/private_include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\esp_hw_support\\CMakeFiles\\__idf_esp_hw_support.dir\\port\\esp32s3\\rtc_init.c.obj -c D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\esp_hw_support\\port\\esp32s3\\rtc_init.c", "file": "D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\esp_hw_support\\port\\esp32s3\\rtc_init.c", "output": "esp-idf\\esp_hw_support\\CMakeFiles\\__idf_esp_hw_support.dir\\port\\esp32s3\\rtc_init.c.obj"}, {"directory": "C:/Users/<USER>/Desktop/i2c_oled/build/bootloader", "command": "D:\\ESP_IDF\\idf_tools\\tools\\xtensa-esp-elf\\esp-13.2.0_20240530\\xtensa-esp-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.4\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/Desktop/i2c_oled/build/bootloader/config -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/dma/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/ldo/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/esp_private -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/log/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_common/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/newlib/platform_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/deprecated_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/efuse/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/efuse/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/spi_flash/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/platform_port/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader_support/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader_support/bootloader_flash/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader_support/private_include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\esp_hw_support\\CMakeFiles\\__idf_esp_hw_support.dir\\port\\esp32s3\\rtc_sleep.c.obj -c D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\esp_hw_support\\port\\esp32s3\\rtc_sleep.c", "file": "D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\esp_hw_support\\port\\esp32s3\\rtc_sleep.c", "output": "esp-idf\\esp_hw_support\\CMakeFiles\\__idf_esp_hw_support.dir\\port\\esp32s3\\rtc_sleep.c.obj"}, {"directory": "C:/Users/<USER>/Desktop/i2c_oled/build/bootloader", "command": "D:\\ESP_IDF\\idf_tools\\tools\\xtensa-esp-elf\\esp-13.2.0_20240530\\xtensa-esp-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.4\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/Desktop/i2c_oled/build/bootloader/config -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/dma/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/ldo/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/esp_private -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/log/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_common/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/newlib/platform_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/deprecated_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/efuse/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/efuse/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/spi_flash/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/platform_port/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader_support/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader_support/bootloader_flash/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader_support/private_include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\esp_hw_support\\CMakeFiles\\__idf_esp_hw_support.dir\\port\\esp32s3\\rtc_time.c.obj -c D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\esp_hw_support\\port\\esp32s3\\rtc_time.c", "file": "D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\esp_hw_support\\port\\esp32s3\\rtc_time.c", "output": "esp-idf\\esp_hw_support\\CMakeFiles\\__idf_esp_hw_support.dir\\port\\esp32s3\\rtc_time.c.obj"}, {"directory": "C:/Users/<USER>/Desktop/i2c_oled/build/bootloader", "command": "D:\\ESP_IDF\\idf_tools\\tools\\xtensa-esp-elf\\esp-13.2.0_20240530\\xtensa-esp-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.4\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/Desktop/i2c_oled/build/bootloader/config -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/dma/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/ldo/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/esp_private -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/log/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_common/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/newlib/platform_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/deprecated_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/efuse/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/efuse/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/spi_flash/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/platform_port/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader_support/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader_support/bootloader_flash/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader_support/private_include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\esp_hw_support\\CMakeFiles\\__idf_esp_hw_support.dir\\port\\esp32s3\\chip_info.c.obj -c D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\esp_hw_support\\port\\esp32s3\\chip_info.c", "file": "D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\esp_hw_support\\port\\esp32s3\\chip_info.c", "output": "esp-idf\\esp_hw_support\\CMakeFiles\\__idf_esp_hw_support.dir\\port\\esp32s3\\chip_info.c.obj"}, {"directory": "C:/Users/<USER>/Desktop/i2c_oled/build/bootloader", "command": "D:\\ESP_IDF\\idf_tools\\tools\\xtensa-esp-elf\\esp-13.2.0_20240530\\xtensa-esp-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.4\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/Desktop/i2c_oled/build/bootloader/config -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_common/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/log/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/dma/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/ldo/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/newlib/platform_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/deprecated_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/efuse/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/efuse/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader_support/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader_support/bootloader_flash/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader_support/private_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/spi_flash/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/platform_port/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\esp_common\\CMakeFiles\\__idf_esp_common.dir\\src\\esp_err_to_name.c.obj -c D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\esp_common\\src\\esp_err_to_name.c", "file": "D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\esp_common\\src\\esp_err_to_name.c", "output": "esp-idf\\esp_common\\CMakeFiles\\__idf_esp_common.dir\\src\\esp_err_to_name.c.obj"}, {"directory": "C:/Users/<USER>/Desktop/i2c_oled/build/bootloader", "command": "D:\\ESP_IDF\\idf_tools\\tools\\xtensa-esp-elf\\esp-13.2.0_20240530\\xtensa-esp-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.4\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/Desktop/i2c_oled/build/bootloader/config -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/log/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_common/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/dma/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/ldo/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/newlib/platform_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/deprecated_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/platform_port/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\esp_rom\\CMakeFiles\\__idf_esp_rom.dir\\patches\\esp_rom_crc.c.obj -c D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\esp_rom\\patches\\esp_rom_crc.c", "file": "D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\esp_rom\\patches\\esp_rom_crc.c", "output": "esp-idf\\esp_rom\\CMakeFiles\\__idf_esp_rom.dir\\patches\\esp_rom_crc.c.obj"}, {"directory": "C:/Users/<USER>/Desktop/i2c_oled/build/bootloader", "command": "D:\\ESP_IDF\\idf_tools\\tools\\xtensa-esp-elf\\esp-13.2.0_20240530\\xtensa-esp-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.4\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/Desktop/i2c_oled/build/bootloader/config -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/log/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_common/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/dma/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/ldo/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/newlib/platform_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/deprecated_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/platform_port/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\esp_rom\\CMakeFiles\\__idf_esp_rom.dir\\patches\\esp_rom_sys.c.obj -c D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\esp_rom\\patches\\esp_rom_sys.c", "file": "D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\esp_rom\\patches\\esp_rom_sys.c", "output": "esp-idf\\esp_rom\\CMakeFiles\\__idf_esp_rom.dir\\patches\\esp_rom_sys.c.obj"}, {"directory": "C:/Users/<USER>/Desktop/i2c_oled/build/bootloader", "command": "D:\\ESP_IDF\\idf_tools\\tools\\xtensa-esp-elf\\esp-13.2.0_20240530\\xtensa-esp-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.4\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/Desktop/i2c_oled/build/bootloader/config -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/log/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_common/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/dma/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/ldo/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/newlib/platform_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/deprecated_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/platform_port/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\esp_rom\\CMakeFiles\\__idf_esp_rom.dir\\patches\\esp_rom_uart.c.obj -c D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\esp_rom\\patches\\esp_rom_uart.c", "file": "D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\esp_rom\\patches\\esp_rom_uart.c", "output": "esp-idf\\esp_rom\\CMakeFiles\\__idf_esp_rom.dir\\patches\\esp_rom_uart.c.obj"}, {"directory": "C:/Users/<USER>/Desktop/i2c_oled/build/bootloader", "command": "D:\\ESP_IDF\\idf_tools\\tools\\xtensa-esp-elf\\esp-13.2.0_20240530\\xtensa-esp-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.4\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/Desktop/i2c_oled/build/bootloader/config -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/log/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_common/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/dma/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/ldo/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/newlib/platform_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/deprecated_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/platform_port/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\esp_rom\\CMakeFiles\\__idf_esp_rom.dir\\patches\\esp_rom_spiflash.c.obj -c D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\esp_rom\\patches\\esp_rom_spiflash.c", "file": "D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\esp_rom\\patches\\esp_rom_spiflash.c", "output": "esp-idf\\esp_rom\\CMakeFiles\\__idf_esp_rom.dir\\patches\\esp_rom_spiflash.c.obj"}, {"directory": "C:/Users/<USER>/Desktop/i2c_oled/build/bootloader", "command": "D:\\ESP_IDF\\idf_tools\\tools\\xtensa-esp-elf\\esp-13.2.0_20240530\\xtensa-esp-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.4\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/Desktop/i2c_oled/build/bootloader/config -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/log/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_common/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/dma/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/ldo/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/newlib/platform_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/deprecated_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/platform_port/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\esp_rom\\CMakeFiles\\__idf_esp_rom.dir\\patches\\esp_rom_efuse.c.obj -c D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\esp_rom\\patches\\esp_rom_efuse.c", "file": "D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\esp_rom\\patches\\esp_rom_efuse.c", "output": "esp-idf\\esp_rom\\CMakeFiles\\__idf_esp_rom.dir\\patches\\esp_rom_efuse.c.obj"}, {"directory": "C:/Users/<USER>/Desktop/i2c_oled/build/bootloader", "command": "D:\\ESP_IDF\\idf_tools\\tools\\xtensa-esp-elf\\esp-13.2.0_20240530\\xtensa-esp-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.4\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/Desktop/i2c_oled/build/bootloader/config -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/log/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_common/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/dma/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/ldo/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/newlib/platform_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/deprecated_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/platform_port/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\esp_rom\\CMakeFiles\\__idf_esp_rom.dir\\patches\\esp_rom_gpio.c.obj -c D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\esp_rom\\patches\\esp_rom_gpio.c", "file": "D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\esp_rom\\patches\\esp_rom_gpio.c", "output": "esp-idf\\esp_rom\\CMakeFiles\\__idf_esp_rom.dir\\patches\\esp_rom_gpio.c.obj"}, {"directory": "C:/Users/<USER>/Desktop/i2c_oled/build/bootloader", "command": "D:\\ESP_IDF\\idf_tools\\tools\\xtensa-esp-elf\\esp-13.2.0_20240530\\xtensa-esp-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.4\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/Desktop/i2c_oled/build/bootloader/config -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/log/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_common/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/dma/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/ldo/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/newlib/platform_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/deprecated_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/platform_port/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/include -mlongcalls  -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -o esp-idf\\esp_rom\\CMakeFiles\\__idf_esp_rom.dir\\patches\\esp_rom_longjmp.S.obj -c D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\esp_rom\\patches\\esp_rom_longjmp.S", "file": "D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\esp_rom\\patches\\esp_rom_longjmp.S", "output": "esp-idf\\esp_rom\\CMakeFiles\\__idf_esp_rom.dir\\patches\\esp_rom_longjmp.S.obj"}, {"directory": "C:/Users/<USER>/Desktop/i2c_oled/build/bootloader", "command": "D:\\ESP_IDF\\idf_tools\\tools\\xtensa-esp-elf\\esp-13.2.0_20240530\\xtensa-esp-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.4\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/Desktop/i2c_oled/build/bootloader/config -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/log/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_common/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/dma/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/ldo/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/newlib/platform_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/deprecated_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/platform_port/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\esp_rom\\CMakeFiles\\__idf_esp_rom.dir\\patches\\esp_rom_systimer.c.obj -c D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\esp_rom\\patches\\esp_rom_systimer.c", "file": "D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\esp_rom\\patches\\esp_rom_systimer.c", "output": "esp-idf\\esp_rom\\CMakeFiles\\__idf_esp_rom.dir\\patches\\esp_rom_systimer.c.obj"}, {"directory": "C:/Users/<USER>/Desktop/i2c_oled/build/bootloader", "command": "D:\\ESP_IDF\\idf_tools\\tools\\xtensa-esp-elf\\esp-13.2.0_20240530\\xtensa-esp-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.4\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/Desktop/i2c_oled/build/bootloader/config -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/log/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_common/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/dma/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/ldo/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/newlib/platform_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/deprecated_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/platform_port/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\esp_rom\\CMakeFiles\\__idf_esp_rom.dir\\patches\\esp_rom_wdt.c.obj -c D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\esp_rom\\patches\\esp_rom_wdt.c", "file": "D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\esp_rom\\patches\\esp_rom_wdt.c", "output": "esp-idf\\esp_rom\\CMakeFiles\\__idf_esp_rom.dir\\patches\\esp_rom_wdt.c.obj"}, {"directory": "C:/Users/<USER>/Desktop/i2c_oled/build/bootloader", "command": "D:\\ESP_IDF\\idf_tools\\tools\\xtensa-esp-elf\\esp-13.2.0_20240530\\xtensa-esp-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.4\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/Desktop/i2c_oled/build/bootloader/config -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/log/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_common/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/dma/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/ldo/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/newlib/platform_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/deprecated_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/platform_port/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\esp_rom\\CMakeFiles\\__idf_esp_rom.dir\\patches\\esp_rom_cache_esp32s2_esp32s3.c.obj -c D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\esp_rom\\patches\\esp_rom_cache_esp32s2_esp32s3.c", "file": "D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\esp_rom\\patches\\esp_rom_cache_esp32s2_esp32s3.c", "output": "esp-idf\\esp_rom\\CMakeFiles\\__idf_esp_rom.dir\\patches\\esp_rom_cache_esp32s2_esp32s3.c.obj"}, {"directory": "C:/Users/<USER>/Desktop/i2c_oled/build/bootloader", "command": "D:\\ESP_IDF\\idf_tools\\tools\\xtensa-esp-elf\\esp-13.2.0_20240530\\xtensa-esp-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.4\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/Desktop/i2c_oled/build/bootloader/config -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/log/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_common/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/dma/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/ldo/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/newlib/platform_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/deprecated_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/platform_port/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/include -mlongcalls  -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -o esp-idf\\esp_rom\\CMakeFiles\\__idf_esp_rom.dir\\patches\\esp_rom_cache_writeback_esp32s3.S.obj -c D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\esp_rom\\patches\\esp_rom_cache_writeback_esp32s3.S", "file": "D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\esp_rom\\patches\\esp_rom_cache_writeback_esp32s3.S", "output": "esp-idf\\esp_rom\\CMakeFiles\\__idf_esp_rom.dir\\patches\\esp_rom_cache_writeback_esp32s3.S.obj"}, {"directory": "C:/Users/<USER>/Desktop/i2c_oled/build/bootloader", "command": "D:\\ESP_IDF\\idf_tools\\tools\\xtensa-esp-elf\\esp-13.2.0_20240530\\xtensa-esp-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.4\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/Desktop/i2c_oled/build/bootloader/config -ID:/ESP_IDF/v5.3.4/esp-idf/components/log/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_common/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/dma/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/ldo/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/newlib/platform_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/deprecated_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/platform_port/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\log\\CMakeFiles\\__idf_log.dir\\log.c.obj -c D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\log\\log.c", "file": "D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\log\\log.c", "output": "esp-idf\\log\\CMakeFiles\\__idf_log.dir\\log.c.obj"}, {"directory": "C:/Users/<USER>/Desktop/i2c_oled/build/bootloader", "command": "D:\\ESP_IDF\\idf_tools\\tools\\xtensa-esp-elf\\esp-13.2.0_20240530\\xtensa-esp-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.4\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/Desktop/i2c_oled/build/bootloader/config -ID:/ESP_IDF/v5.3.4/esp-idf/components/log/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_common/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/dma/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/ldo/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/newlib/platform_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/deprecated_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/platform_port/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\log\\CMakeFiles\\__idf_log.dir\\log_buffers.c.obj -c D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\log\\log_buffers.c", "file": "D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\log\\log_buffers.c", "output": "esp-idf\\log\\CMakeFiles\\__idf_log.dir\\log_buffers.c.obj"}, {"directory": "C:/Users/<USER>/Desktop/i2c_oled/build/bootloader", "command": "D:\\ESP_IDF\\idf_tools\\tools\\xtensa-esp-elf\\esp-13.2.0_20240530\\xtensa-esp-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.4\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/Desktop/i2c_oled/build/bootloader/config -ID:/ESP_IDF/v5.3.4/esp-idf/components/log/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_common/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/dma/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/ldo/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/newlib/platform_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/deprecated_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/platform_port/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/hal/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\log\\CMakeFiles\\__idf_log.dir\\log_noos.c.obj -c D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\log\\log_noos.c", "file": "D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\log\\log_noos.c", "output": "esp-idf\\log\\CMakeFiles\\__idf_log.dir\\log_noos.c.obj"}, {"directory": "C:/Users/<USER>/Desktop/i2c_oled/build/bootloader", "command": "D:\\ESP_IDF\\idf_tools\\tools\\xtensa-esp-elf\\esp-13.2.0_20240530\\xtensa-esp-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.3.4\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/Desktop/i2c_oled/build/bootloader/config -ID:/ESP_IDF/v5.3.4/esp-idf/components/log/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/include/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_rom/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_common/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/dma/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/ldo/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/ESP_IDF/v5.3.4/esp-idf/components/esp_hw_support/port/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/newlib/platform_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/xtensa/deprecated_include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3 -ID:/ESP_IDF/v5.3.4/esp-idf/components/soc/esp32s3/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader_support/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader_support/bootloader_flash/include -ID:/ESP_IDF/v5.3.4/esp-idf/components/bootloader_support/private_include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/ESP_IDF/v5.3.4/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\main\\CMakeFiles\\__idf_main.dir\\bootloader_start.c.obj -c D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\bootloader\\subproject\\main\\bootloader_start.c", "file": "D:\\ESP_IDF\\v5.3.4\\esp-idf\\components\\bootloader\\subproject\\main\\bootloader_start.c", "output": "esp-idf\\main\\CMakeFiles\\__idf_main.dir\\bootloader_start.c.obj"}]